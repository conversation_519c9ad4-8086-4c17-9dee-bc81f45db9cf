import tkinter as tk
from tkinter import ttk, messagebox
from database import db_manager
from datetime import datetime, date

class UserManagement:
    def __init__(self, main_app):
        self.main_app = main_app
    
    def show_user_management(self):
        """Show user management interface"""
        self.main_app.clear_content()
        
        # Title
        title_label = ttk.Label(self.main_app.content_frame, text="User Management", style='Title.TLabel')
        title_label.pack(pady=10)
        
        # Notebook for tabs
        notebook = ttk.Notebook(self.main_app.content_frame)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_user_tab = ttk.Frame(notebook)
        self.manage_users_tab = ttk.Frame(notebook)
        self.manage_students_tab = ttk.Frame(notebook)
        self.manage_teachers_tab = ttk.Frame(notebook)
        
        notebook.add(self.create_user_tab, text="Create User")
        notebook.add(self.manage_users_tab, text="Manage Users")
        notebook.add(self.manage_students_tab, text="Students")
        notebook.add(self.manage_teachers_tab, text="Teachers")
        
        # Setup tabs
        self.setup_create_user_tab()
        self.setup_manage_users_tab()
        self.setup_students_tab()
        self.setup_teachers_tab()
    
    def setup_create_user_tab(self):
        """Setup create user tab"""
        # Main frame
        main_frame = ttk.Frame(self.create_user_tab)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # User form
        form_frame = ttk.LabelFrame(main_frame, text="Create New User", padding=20)
        form_frame.pack(fill='x', pady=10)
        
        # Form fields
        fields = [
            ("Username:", "username"),
            ("Password:", "password"),
            ("Full Name:", "full_name"),
            ("Email:", "email"),
            ("Phone:", "phone"),
            ("Address:", "address")
        ]
        
        self.create_user_entries = {}
        
        for i, (label, field) in enumerate(fields):
            ttk.Label(form_frame, text=label).grid(row=i, column=0, sticky='e', padx=5, pady=5)
            
            if field == "password":
                entry = ttk.Entry(form_frame, width=30, show='*')
            elif field == "address":
                entry = tk.Text(form_frame, width=30, height=3)
            else:
                entry = ttk.Entry(form_frame, width=30)
            
            entry.grid(row=i, column=1, padx=5, pady=5, sticky='w')
            self.create_user_entries[field] = entry
        
        # Role selection
        ttk.Label(form_frame, text="Role:").grid(row=len(fields), column=0, sticky='e', padx=5, pady=5)
        self.role_var = tk.StringVar(value="student")
        role_combo = ttk.Combobox(form_frame, textvariable=self.role_var, 
                                 values=["admin", "teacher", "student"], state="readonly", width=27)
        role_combo.grid(row=len(fields), column=1, padx=5, pady=5, sticky='w')
        
        # Additional fields frame (shown based on role)
        self.additional_frame = ttk.LabelFrame(main_frame, text="Additional Information", padding=20)
        self.additional_frame.pack(fill='x', pady=10)
        
        # Role change handler
        role_combo.bind('<<ComboboxSelected>>', self.on_role_change)
        
        # Create button
        create_btn = ttk.Button(main_frame, text="Create User", command=self.create_user)
        create_btn.pack(pady=20)
        
        # Initialize additional fields
        self.on_role_change()
    
    def on_role_change(self, event=None):
        """Handle role change to show additional fields"""
        # Clear additional frame
        for widget in self.additional_frame.winfo_children():
            widget.destroy()
        
        role = self.role_var.get()
        self.additional_entries = {}
        
        if role == "student":
            fields = [
                ("Student ID:", "student_id"),
                ("Class:", "class_name"),
                ("Section:", "section"),
                ("Roll Number:", "roll_number"),
                ("Date of Birth:", "date_of_birth"),
                ("Guardian Name:", "guardian_name"),
                ("Guardian Phone:", "guardian_phone")
            ]
            
            for i, (label, field) in enumerate(fields):
                ttk.Label(self.additional_frame, text=label).grid(row=i, column=0, sticky='e', padx=5, pady=5)
                
                if field == "date_of_birth":
                    entry = ttk.Entry(self.additional_frame, width=30)
                    ttk.Label(self.additional_frame, text="(YYYY-MM-DD)", font=('Arial', 8)).grid(row=i, column=2, sticky='w')
                else:
                    entry = ttk.Entry(self.additional_frame, width=30)
                
                entry.grid(row=i, column=1, padx=5, pady=5, sticky='w')
                self.additional_entries[field] = entry
        
        elif role == "teacher":
            fields = [
                ("Teacher ID:", "teacher_id"),
                ("Department:", "department"),
                ("Qualification:", "qualification"),
                ("Experience (Years):", "experience_years"),
                ("Salary:", "salary")
            ]
            
            for i, (label, field) in enumerate(fields):
                ttk.Label(self.additional_frame, text=label).grid(row=i, column=0, sticky='e', padx=5, pady=5)
                entry = ttk.Entry(self.additional_frame, width=30)
                entry.grid(row=i, column=1, padx=5, pady=5, sticky='w')
                self.additional_entries[field] = entry
    
    def create_user(self):
        """Create new user"""
        try:
            # Get basic user data
            username = self.create_user_entries["username"].get().strip()
            password = self.create_user_entries["password"].get().strip()
            full_name = self.create_user_entries["full_name"].get().strip()
            email = self.create_user_entries["email"].get().strip()
            phone = self.create_user_entries["phone"].get().strip()
            
            # Get address (Text widget)
            address = self.create_user_entries["address"].get("1.0", tk.END).strip()
            
            role = self.role_var.get()
            
            # Validation
            if not all([username, password, full_name, role]):
                messagebox.showerror("Error", "Please fill in all required fields")
                return
            
            # Create user
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            
            # Check if username exists
            cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
            if cursor.fetchone():
                messagebox.showerror("Error", "Username already exists")
                conn.close()
                return
            
            # Insert user
            password_hash = db_manager.hash_password(password)
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name, email, phone, address)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (username, password_hash, role, full_name, email, phone, address))
            
            user_id = cursor.lastrowid
            
            # Insert role-specific data
            if role == "student" and hasattr(self, 'additional_entries'):
                student_data = {}
                for field, entry in self.additional_entries.items():
                    value = entry.get().strip()
                    if value:
                        student_data[field] = value
                
                if 'student_id' in student_data:
                    cursor.execute('''
                        INSERT INTO students (user_id, student_id, class_name, section, roll_number, 
                                            date_of_birth, guardian_name, guardian_phone)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (user_id, student_data.get('student_id'), student_data.get('class_name'),
                          student_data.get('section'), student_data.get('roll_number'),
                          student_data.get('date_of_birth'), student_data.get('guardian_name'),
                          student_data.get('guardian_phone')))
            
            elif role == "teacher" and hasattr(self, 'additional_entries'):
                teacher_data = {}
                for field, entry in self.additional_entries.items():
                    value = entry.get().strip()
                    if value:
                        teacher_data[field] = value
                
                if 'teacher_id' in teacher_data:
                    cursor.execute('''
                        INSERT INTO teachers (user_id, teacher_id, department, qualification, 
                                            experience_years, salary)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (user_id, teacher_data.get('teacher_id'), teacher_data.get('department'),
                          teacher_data.get('qualification'), teacher_data.get('experience_years'),
                          teacher_data.get('salary')))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("Success", f"{role.title()} created successfully!")
            
            # Clear form
            for entry in self.create_user_entries.values():
                if isinstance(entry, tk.Text):
                    entry.delete("1.0", tk.END)
                else:
                    entry.delete(0, tk.END)
            
            if hasattr(self, 'additional_entries'):
                for entry in self.additional_entries.values():
                    entry.delete(0, tk.END)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create user: {str(e)}")
    
    def setup_manage_users_tab(self):
        """Setup manage users tab"""
        # Search and filter frame
        search_frame = ttk.LabelFrame(self.manage_users_tab, text="Search & Filter", padding=10)
        search_frame.pack(fill='x', padx=10, pady=5)

        # Search by name/username
        ttk.Label(search_frame, text="Search:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.grid(row=0, column=1, padx=5, pady=5)
        search_entry.bind('<KeyRelease>', lambda e: self.filter_users())

        # Filter by role
        ttk.Label(search_frame, text="Role:").grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.role_filter_var = tk.StringVar(value="All")
        role_combo = ttk.Combobox(search_frame, textvariable=self.role_filter_var,
                                 values=["All", "Admin", "Teacher", "Student"], state="readonly", width=15)
        role_combo.grid(row=0, column=3, padx=5, pady=5)
        role_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_users())

        # Filter by status
        ttk.Label(search_frame, text="Status:").grid(row=0, column=4, sticky='e', padx=5, pady=5)
        self.status_filter_var = tk.StringVar(value="All")
        status_combo = ttk.Combobox(search_frame, textvariable=self.status_filter_var,
                                   values=["All", "Active", "Inactive"], state="readonly", width=15)
        status_combo.grid(row=0, column=5, padx=5, pady=5)
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_users())

        # Clear filters button
        ttk.Button(search_frame, text="Clear Filters", command=self.clear_filters).grid(row=0, column=6, padx=10, pady=5)

        # Users list
        list_frame = ttk.Frame(self.manage_users_tab)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for users
        columns = ("ID", "Username", "Role", "Full Name", "Email", "Status")
        self.users_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=120)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        self.users_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Buttons frame
        btn_frame = ttk.Frame(self.manage_users_tab)
        btn_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(btn_frame, text="Refresh", command=self.refresh_users).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="Activate User", command=self.activate_user).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="Deactivate User", command=self.deactivate_user).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="Edit User", command=self.edit_user).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="Reset Password", command=self.reset_user_password).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="Delete User", command=self.delete_user).pack(side='left', padx=5)

        # Bulk operations frame
        bulk_frame = ttk.LabelFrame(self.manage_users_tab, text="Bulk Operations", padding=10)
        bulk_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(bulk_frame, text="Export Users", command=self.export_users).pack(side='left', padx=5)
        ttk.Button(bulk_frame, text="Bulk Activate", command=self.bulk_activate).pack(side='left', padx=5)
        ttk.Button(bulk_frame, text="Bulk Deactivate", command=self.bulk_deactivate).pack(side='left', padx=5)
        
        # Load users
        self.refresh_users()
    
    def refresh_users(self):
        """Refresh users list"""
        # Clear existing items
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        # Load users
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, role, full_name, email, is_active
            FROM users ORDER BY role, full_name
        ''')
        
        users = cursor.fetchall()
        conn.close()
        
        for user in users:
            status = "Active" if user[5] else "Inactive"
            self.users_tree.insert('', 'end', values=user[:5] + (status,))
    
    def activate_user(self):
        """Activate selected user"""
        self.change_user_status(True)
    
    def deactivate_user(self):
        """Deactivate selected user"""
        self.change_user_status(False)
    
    def change_user_status(self, status):
        """Change user status"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a user")
            return
        
        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        username = item['values'][1]
        
        if username == 'admin' and not status:
            messagebox.showerror("Error", "Cannot deactivate admin user")
            return
        
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("UPDATE users SET is_active = ? WHERE id = ?", (status, user_id))
        conn.commit()
        conn.close()
        
        action = "activated" if status else "deactivated"
        messagebox.showinfo("Success", f"User {action} successfully!")
        self.refresh_users()

    def edit_user(self):
        """Edit selected user"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a user to edit")
            return

        item = self.users_tree.item(selection[0])
        user_data = item['values']
        user_id = user_data[0]

        # Create edit window
        self.edit_window = tk.Toplevel(self.main_app.root)
        self.edit_window.title("Edit User")
        self.edit_window.geometry("500x400")
        self.edit_window.transient(self.main_app.root)
        self.edit_window.grab_set()

        # Center the window
        self.edit_window.update_idletasks()
        x = (self.edit_window.winfo_screenwidth() // 2) - (250)
        y = (self.edit_window.winfo_screenheight() // 2) - (200)
        self.edit_window.geometry(f"500x400+{x}+{y}")

        # Get current user data
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT username, full_name, email, phone, address, role
            FROM users WHERE id = ?
        ''', (user_id,))

        current_data = cursor.fetchone()
        conn.close()

        if not current_data:
            messagebox.showerror("Error", "User not found")
            self.edit_window.destroy()
            return

        # Edit form
        main_frame = ttk.Frame(self.edit_window)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        ttk.Label(main_frame, text="Edit User Information", font=('Arial', 14, 'bold')).pack(pady=10)

        # Form fields
        fields = [
            ("Username:", "username"),
            ("Full Name:", "full_name"),
            ("Email:", "email"),
            ("Phone:", "phone"),
            ("Address:", "address")
        ]

        self.edit_entries = {}

        for i, (label, field) in enumerate(fields):
            ttk.Label(main_frame, text=label).grid(row=i, column=0, sticky='e', padx=5, pady=5)

            if field == "address":
                entry = tk.Text(main_frame, width=30, height=3)
                entry.insert("1.0", current_data[4] or "")
            else:
                entry = ttk.Entry(main_frame, width=30)
                entry.insert(0, current_data[fields.index((label, field))] or "")

            entry.grid(row=i, column=1, padx=5, pady=5, sticky='w')
            self.edit_entries[field] = entry

        # Role (read-only for safety)
        ttk.Label(main_frame, text="Role:").grid(row=len(fields), column=0, sticky='e', padx=5, pady=5)
        role_label = ttk.Label(main_frame, text=current_data[5].title(), font=('Arial', 10, 'bold'))
        role_label.grid(row=len(fields), column=1, sticky='w', padx=5, pady=5)

        # Buttons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        ttk.Button(btn_frame, text="Save Changes",
                  command=lambda: self.save_user_changes(user_id)).pack(side='left', padx=10)
        ttk.Button(btn_frame, text="Cancel",
                  command=self.edit_window.destroy).pack(side='left', padx=10)

    def save_user_changes(self, user_id):
        """Save user changes"""
        try:
            # Get form data
            username = self.edit_entries["username"].get().strip()
            full_name = self.edit_entries["full_name"].get().strip()
            email = self.edit_entries["email"].get().strip()
            phone = self.edit_entries["phone"].get().strip()
            address = self.edit_entries["address"].get("1.0", tk.END).strip()

            # Validation
            if not all([username, full_name]):
                messagebox.showerror("Error", "Username and Full Name are required")
                return

            # Update user
            conn = db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE users
                SET username = ?, full_name = ?, email = ?, phone = ?, address = ?
                WHERE id = ?
            ''', (username, full_name, email, phone, address, user_id))

            conn.commit()
            conn.close()

            messagebox.showinfo("Success", "User updated successfully!")
            self.edit_window.destroy()
            self.refresh_users()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to update user: {str(e)}")

    def reset_user_password(self):
        """Reset user password"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a user")
            return

        item = self.users_tree.item(selection[0])
        user_data = item['values']
        user_id = user_data[0]
        username = user_data[1]

        # Confirm reset
        if not messagebox.askyesno("Confirm Reset",
                                  f"Reset password for user '{username}'?\nNew password will be 'password123'"):
            return

        try:
            conn = db_manager.get_connection()
            cursor = conn.cursor()

            new_password_hash = db_manager.hash_password("password123")
            cursor.execute("UPDATE users SET password_hash = ? WHERE id = ?",
                          (new_password_hash, user_id))

            conn.commit()
            conn.close()

            messagebox.showinfo("Success",
                               f"Password reset successfully!\nNew password: password123\nPlease inform the user to change it.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to reset password: {str(e)}")

    def delete_user(self):
        """Delete selected user"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a user to delete")
            return

        item = self.users_tree.item(selection[0])
        user_data = item['values']
        user_id = user_data[0]
        username = user_data[1]

        if username == 'admin':
            messagebox.showerror("Error", "Cannot delete admin user")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm Deletion",
                                  f"Are you sure you want to delete user '{username}'?\n\nThis action cannot be undone and will remove all associated data."):
            return

        try:
            conn = db_manager.get_connection()
            cursor = conn.cursor()

            # Delete user (cascade will handle related records)
            cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))

            conn.commit()
            conn.close()

            messagebox.showinfo("Success", "User deleted successfully!")
            self.refresh_users()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete user: {str(e)}")

    def filter_users(self):
        """Filter users based on search criteria"""
        search_text = self.search_var.get().lower()
        role_filter = self.role_filter_var.get()
        status_filter = self.status_filter_var.get()

        # Clear existing items
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)

        # Build query with filters
        query = '''
            SELECT id, username, role, full_name, email, is_active
            FROM users WHERE 1=1
        '''
        params = []

        # Apply search filter
        if search_text:
            query += " AND (LOWER(username) LIKE ? OR LOWER(full_name) LIKE ?)"
            params.extend([f"%{search_text}%", f"%{search_text}%"])

        # Apply role filter
        if role_filter != "All":
            query += " AND role = ?"
            params.append(role_filter.lower())

        # Apply status filter
        if status_filter != "All":
            is_active = 1 if status_filter == "Active" else 0
            query += " AND is_active = ?"
            params.append(is_active)

        query += " ORDER BY role, full_name"

        # Execute query
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        users = cursor.fetchall()
        conn.close()

        # Populate filtered results
        for user in users:
            status = "Active" if user[5] else "Inactive"
            self.users_tree.insert('', 'end', values=user[:5] + (status,))

    def clear_filters(self):
        """Clear all filters and refresh"""
        self.search_var.set("")
        self.role_filter_var.set("All")
        self.status_filter_var.set("All")
        self.refresh_users()

    def export_users(self):
        """Export users to text file"""
        try:
            conn = db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT username, role, full_name, email, phone,
                       CASE WHEN is_active = 1 THEN 'Active' ELSE 'Inactive' END as status,
                       created_at
                FROM users ORDER BY role, full_name
            ''')

            users = cursor.fetchall()
            conn.close()

            # Create export content
            export_content = "USER EXPORT REPORT\n"
            export_content += "=" * 80 + "\n"
            export_content += f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            export_content += f"Total Users: {len(users)}\n\n"

            export_content += f"{'Username':<15} {'Role':<10} {'Full Name':<25} {'Email':<25} {'Status':<10}\n"
            export_content += "-" * 90 + "\n"

            for user in users:
                export_content += f"{user[0]:<15} {user[1].title():<10} {user[2]:<25} {(user[3] or 'N/A'):<25} {user[5]:<10}\n"

            # Save to file
            filename = f"users_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(export_content)

            messagebox.showinfo("Export Complete", f"Users exported to {filename}")

        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export users: {str(e)}")

    def bulk_activate(self):
        """Bulk activate users by role"""
        self.bulk_change_status(True)

    def bulk_deactivate(self):
        """Bulk deactivate users by role"""
        self.bulk_change_status(False)

    def bulk_change_status(self, status):
        """Bulk change user status"""
        # Create selection dialog
        dialog = tk.Toplevel(self.main_app.root)
        dialog.title("Bulk Status Change")
        dialog.geometry("300x200")
        dialog.transient(self.main_app.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - 150
        y = (dialog.winfo_screenheight() // 2) - 100
        dialog.geometry(f"300x200+{x}+{y}")

        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        action = "Activate" if status else "Deactivate"
        ttk.Label(main_frame, text=f"Bulk {action} Users", font=('Arial', 12, 'bold')).pack(pady=10)

        ttk.Label(main_frame, text="Select role to update:").pack(pady=5)

        role_var = tk.StringVar(value="student")
        for role in ["student", "teacher"]:  # Don't allow bulk admin changes
            ttk.Radiobutton(main_frame, text=role.title(), variable=role_var, value=role).pack(anchor='w')

        def execute_bulk_change():
            selected_role = role_var.get()

            if not messagebox.askyesno("Confirm Bulk Operation",
                                      f"{action} all {selected_role}s?\n\nThis will affect multiple users."):
                return

            try:
                conn = db_manager.get_connection()
                cursor = conn.cursor()

                cursor.execute("UPDATE users SET is_active = ? WHERE role = ?", (status, selected_role))
                affected_rows = cursor.rowcount

                conn.commit()
                conn.close()

                messagebox.showinfo("Success", f"{action}d {affected_rows} {selected_role}(s) successfully!")
                dialog.destroy()
                self.refresh_users()

            except Exception as e:
                messagebox.showerror("Error", f"Bulk operation failed: {str(e)}")

        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=20)

        ttk.Button(btn_frame, text=f"{action} Users", command=execute_bulk_change).pack(side='left', padx=10)
        ttk.Button(btn_frame, text="Cancel", command=dialog.destroy).pack(side='left', padx=10)
    
    def setup_students_tab(self):
        """Setup students management tab"""
        # Students list
        list_frame = ttk.Frame(self.manage_students_tab)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for students
        columns = ("ID", "Student ID", "Name", "Class", "Section", "Roll No", "Guardian")
        self.students_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.students_tree.heading(col, text=col)
            self.students_tree.column(col, width=100)
        
        # Scrollbar
        scrollbar2 = ttk.Scrollbar(list_frame, orient='vertical', command=self.students_tree.yview)
        self.students_tree.configure(yscrollcommand=scrollbar2.set)
        
        self.students_tree.pack(side='left', fill='both', expand=True)
        scrollbar2.pack(side='right', fill='y')
        
        # Buttons
        btn_frame2 = ttk.Frame(self.manage_students_tab)
        btn_frame2.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(btn_frame2, text="Refresh", command=self.refresh_students).pack(side='left', padx=5)
        
        # Load students
        self.refresh_students()
    
    def refresh_students(self):
        """Refresh students list"""
        # Clear existing items
        for item in self.students_tree.get_children():
            self.students_tree.delete(item)
        
        # Load students
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT s.id, s.student_id, u.full_name, s.class_name, s.section, 
                   s.roll_number, s.guardian_name
            FROM students s
            JOIN users u ON s.user_id = u.id
            WHERE u.is_active = 1
            ORDER BY s.class_name, s.section, s.roll_number
        ''')
        
        students = cursor.fetchall()
        conn.close()
        
        for student in students:
            self.students_tree.insert('', 'end', values=student)
    
    def setup_teachers_tab(self):
        """Setup teachers management tab"""
        # Teachers list
        list_frame = ttk.Frame(self.manage_teachers_tab)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for teachers
        columns = ("ID", "Teacher ID", "Name", "Department", "Qualification", "Experience")
        self.teachers_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.teachers_tree.heading(col, text=col)
            self.teachers_tree.column(col, width=120)
        
        # Scrollbar
        scrollbar3 = ttk.Scrollbar(list_frame, orient='vertical', command=self.teachers_tree.yview)
        self.teachers_tree.configure(yscrollcommand=scrollbar3.set)
        
        self.teachers_tree.pack(side='left', fill='both', expand=True)
        scrollbar3.pack(side='right', fill='y')
        
        # Buttons
        btn_frame3 = ttk.Frame(self.manage_teachers_tab)
        btn_frame3.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(btn_frame3, text="Refresh", command=self.refresh_teachers).pack(side='left', padx=5)
        
        # Load teachers
        self.refresh_teachers()
    
    def refresh_teachers(self):
        """Refresh teachers list"""
        # Clear existing items
        for item in self.teachers_tree.get_children():
            self.teachers_tree.delete(item)
        
        # Load teachers
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT t.id, t.teacher_id, u.full_name, t.department, t.qualification, t.experience_years
            FROM teachers t
            JOIN users u ON t.user_id = u.id
            WHERE u.is_active = 1
            ORDER BY t.department, u.full_name
        ''')
        
        teachers = cursor.fetchall()
        conn.close()
        
        for teacher in teachers:
            self.teachers_tree.insert('', 'end', values=teacher)
