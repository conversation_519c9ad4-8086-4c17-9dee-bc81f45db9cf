import tkinter as tk
from tkinter import ttk, messagebox
from database import db_manager
from datetime import datetime, date
import calendar

class FinancialManagement:
    def __init__(self, main_app):
        self.main_app = main_app
    
    def show_financial_management(self):
        """Show financial management interface for admin"""
        self.main_app.clear_content()
        
        # Title
        title_label = ttk.Label(self.main_app.content_frame, text="Financial Management", style='Title.TLabel')
        title_label.pack(pady=10)
        
        # Notebook for tabs
        notebook = ttk.Notebook(self.main_app.content_frame)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs
        self.fee_structure_tab = ttk.Frame(notebook)
        self.payments_tab = ttk.Frame(notebook)
        self.reports_tab = ttk.Frame(notebook)
        self.reminders_tab = ttk.Frame(notebook)
        
        notebook.add(self.fee_structure_tab, text="Fee Structure")
        notebook.add(self.payments_tab, text="Payments")
        notebook.add(self.reports_tab, text="Reports")
        notebook.add(self.reminders_tab, text="Pending Payments")

        # Add new tab for outstanding amounts
        self.outstanding_tab = ttk.Frame(notebook)
        notebook.add(self.outstanding_tab, text="Outstanding Amounts")
        
        # Setup tabs
        self.setup_fee_structure_tab()
        self.setup_payments_tab()
        self.setup_reports_tab()
        self.setup_reminders_tab()
        self.setup_outstanding_tab()
    
    def setup_fee_structure_tab(self):
        """Setup fee structure management tab"""
        # Create fee structure form
        form_frame = ttk.LabelFrame(self.fee_structure_tab, text="Add Fee Structure", padding=20)
        form_frame.pack(fill='x', padx=10, pady=10)
        
        # Form fields
        fields = [
            ("Class:", "class_name"),
            ("Fee Type:", "fee_type"),
            ("Amount:", "amount"),
            ("Due Date (Day of Month):", "due_date_day"),
            ("Academic Year:", "academic_year")
        ]
        
        self.fee_entries = {}
        
        for i, (label, field) in enumerate(fields):
            ttk.Label(form_frame, text=label).grid(row=i, column=0, sticky='e', padx=5, pady=5)
            
            if field == "fee_type":
                entry = ttk.Combobox(form_frame, values=["Tuition Fee", "Admission Fee", "Exam Fee", 
                                                       "Library Fee", "Lab Fee", "Sports Fee"], 
                                   state="readonly", width=27)
            elif field == "due_date_day":
                entry = ttk.Combobox(form_frame, values=list(range(1, 32)), state="readonly", width=27)
            else:
                entry = ttk.Entry(form_frame, width=30)
            
            entry.grid(row=i, column=1, padx=5, pady=5, sticky='w')
            self.fee_entries[field] = entry
        
        # Set default academic year
        current_year = datetime.now().year
        self.fee_entries["academic_year"].insert(0, f"{current_year}-{current_year+1}")
        
        # Add button
        add_fee_btn = ttk.Button(form_frame, text="Add Fee Structure", command=self.add_fee_structure)
        add_fee_btn.grid(row=len(fields), column=0, columnspan=2, pady=20)
        
        # Fee structures list
        list_frame = ttk.Frame(self.fee_structure_tab)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for fee structures
        columns = ("ID", "Class", "Fee Type", "Amount", "Due Day", "Academic Year", "Status")
        self.fee_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.fee_tree.heading(col, text=col)
            self.fee_tree.column(col, width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.fee_tree.yview)
        self.fee_tree.configure(yscrollcommand=scrollbar.set)
        
        self.fee_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Buttons frame
        btn_frame = ttk.Frame(self.fee_structure_tab)
        btn_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(btn_frame, text="Refresh", command=self.refresh_fee_structures).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="Activate", command=self.activate_fee_structure).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="Deactivate", command=self.deactivate_fee_structure).pack(side='left', padx=5)
        
        # Load fee structures
        self.refresh_fee_structures()
    
    def add_fee_structure(self):
        """Add new fee structure"""
        try:
            # Get form data
            class_name = self.fee_entries["class_name"].get().strip()
            fee_type = self.fee_entries["fee_type"].get().strip()
            amount = self.fee_entries["amount"].get().strip()
            due_date_day = self.fee_entries["due_date_day"].get().strip()
            academic_year = self.fee_entries["academic_year"].get().strip()
            
            # Validation
            if not all([class_name, fee_type, amount, academic_year]):
                messagebox.showerror("Error", "Please fill in all required fields")
                return
            
            try:
                amount = float(amount)
                due_date_day = int(due_date_day) if due_date_day else 10
            except ValueError:
                messagebox.showerror("Error", "Invalid amount or due date")
                return
            
            # Insert fee structure
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO fee_structure (class_name, fee_type, amount, due_date_day, academic_year)
                VALUES (?, ?, ?, ?, ?)
            ''', (class_name, fee_type, amount, due_date_day, academic_year))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("Success", "Fee structure added successfully!")
            
            # Clear form
            for field, entry in self.fee_entries.items():
                if field != "academic_year":  # Keep academic year
                    if hasattr(entry, 'delete'):
                        entry.delete(0, tk.END)
                    else:
                        entry.set("")
            
            # Refresh list
            self.refresh_fee_structures()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add fee structure: {str(e)}")
    
    def refresh_fee_structures(self):
        """Refresh fee structures list"""
        # Clear existing items
        for item in self.fee_tree.get_children():
            self.fee_tree.delete(item)
        
        # Load fee structures
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, class_name, fee_type, amount, due_date_day, academic_year, is_active
            FROM fee_structure
            ORDER BY academic_year DESC, class_name, fee_type
        ''')
        
        fee_structures = cursor.fetchall()
        conn.close()
        
        for fee in fee_structures:
            status = "Active" if fee[6] else "Inactive"
            self.fee_tree.insert('', 'end', values=fee[:6] + (status,))
    
    def activate_fee_structure(self):
        """Activate selected fee structure"""
        self.change_fee_structure_status(True)
    
    def deactivate_fee_structure(self):
        """Deactivate selected fee structure"""
        self.change_fee_structure_status(False)
    
    def change_fee_structure_status(self, status):
        """Change fee structure status"""
        selection = self.fee_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a fee structure")
            return
        
        item = self.fee_tree.item(selection[0])
        fee_id = item['values'][0]
        
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("UPDATE fee_structure SET is_active = ? WHERE id = ?", (status, fee_id))
        conn.commit()
        conn.close()
        
        action = "activated" if status else "deactivated"
        messagebox.showinfo("Success", f"Fee structure {action} successfully!")
        self.refresh_fee_structures()
    
    def setup_payments_tab(self):
        """Setup payments management tab"""
        # Payment form
        form_frame = ttk.LabelFrame(self.payments_tab, text="Record Payment", padding=20)
        form_frame.pack(fill='x', padx=10, pady=10)
        
        # Student selection
        ttk.Label(form_frame, text="Student:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.student_var = tk.StringVar()
        self.student_combo = ttk.Combobox(form_frame, textvariable=self.student_var, state="readonly", width=30)
        self.student_combo.grid(row=0, column=1, padx=5, pady=5, sticky='w')
        self.student_combo.bind('<<ComboboxSelected>>', self.on_student_selected)
        
        # Fee structure selection
        ttk.Label(form_frame, text="Fee Type:").grid(row=1, column=0, sticky='e', padx=5, pady=5)
        self.fee_structure_var = tk.StringVar()
        self.fee_structure_combo = ttk.Combobox(form_frame, textvariable=self.fee_structure_var, 
                                              state="readonly", width=30)
        self.fee_structure_combo.grid(row=1, column=1, padx=5, pady=5, sticky='w')
        
        # Payment fields
        payment_fields = [
            ("Amount Paid:", "amount_paid"),
            ("Payment Method:", "payment_method"),
            ("Receipt Number:", "receipt_number"),
            ("Remarks:", "remarks")
        ]
        
        self.payment_entries = {}
        
        for i, (label, field) in enumerate(payment_fields, start=2):
            ttk.Label(form_frame, text=label).grid(row=i, column=0, sticky='e', padx=5, pady=5)
            
            if field == "payment_method":
                entry = ttk.Combobox(form_frame, values=["Cash", "Bank Transfer", "Cheque", "Online"], 
                                   state="readonly", width=27)
            else:
                entry = ttk.Entry(form_frame, width=30)
            
            entry.grid(row=i, column=1, padx=5, pady=5, sticky='w')
            self.payment_entries[field] = entry
        
        # Record button
        record_btn = ttk.Button(form_frame, text="Record Payment", command=self.record_payment)
        record_btn.grid(row=len(payment_fields)+2, column=0, columnspan=2, pady=20)
        
        # Payments list
        list_frame = ttk.Frame(self.payments_tab)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for payments
        columns = ("ID", "Student", "Fee Type", "Amount", "Date", "Method", "Receipt", "Recorded By")
        self.payments_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.payments_tree.heading(col, text=col)
            self.payments_tree.column(col, width=100)
        
        # Scrollbar
        scrollbar2 = ttk.Scrollbar(list_frame, orient='vertical', command=self.payments_tree.yview)
        self.payments_tree.configure(yscrollcommand=scrollbar2.set)
        
        self.payments_tree.pack(side='left', fill='both', expand=True)
        scrollbar2.pack(side='right', fill='y')
        
        # Load data
        self.load_students_for_payment()
        self.refresh_payments()
    
    def load_students_for_payment(self):
        """Load students for payment dropdown"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT s.id, u.full_name, s.student_id, s.class_name
            FROM students s
            JOIN users u ON s.user_id = u.id
            WHERE u.is_active = 1
            ORDER BY s.class_name, u.full_name
        ''')
        
        students = cursor.fetchall()
        conn.close()
        
        student_list = [f"{student[1]} ({student[2]}) - {student[3]}" for student in students]
        self.student_combo['values'] = student_list
        self.student_data = {f"{student[1]} ({student[2]}) - {student[3]}": student for student in students}
    
    def on_student_selected(self, event=None):
        """Handle student selection to load applicable fees"""
        student_selection = self.student_var.get()
        if not student_selection or student_selection not in self.student_data:
            return
        
        student = self.student_data[student_selection]
        class_name = student[3]
        
        # Load fee structures for this class
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, fee_type, amount
            FROM fee_structure
            WHERE class_name = ? AND is_active = 1
            ORDER BY fee_type
        ''', (class_name,))
        
        fee_structures = cursor.fetchall()
        conn.close()
        
        fee_list = [f"{fee[1]} - ${fee[2]}" for fee in fee_structures]
        self.fee_structure_combo['values'] = fee_list
        self.fee_structure_data = {f"{fee[1]} - ${fee[2]}": fee for fee in fee_structures}
    
    def record_payment(self):
        """Record new payment"""
        try:
            # Get form data
            student_selection = self.student_var.get()
            fee_selection = self.fee_structure_var.get()
            amount_paid = self.payment_entries["amount_paid"].get().strip()
            payment_method = self.payment_entries["payment_method"].get().strip()
            receipt_number = self.payment_entries["receipt_number"].get().strip()
            remarks = self.payment_entries["remarks"].get().strip()
            
            # Validation
            if not all([student_selection, fee_selection, amount_paid]):
                messagebox.showerror("Error", "Please fill in required fields")
                return
            
            try:
                amount_paid = float(amount_paid)
            except ValueError:
                messagebox.showerror("Error", "Invalid amount")
                return
            
            student = self.student_data[student_selection]
            fee_structure = self.fee_structure_data[fee_selection]
            
            # Insert payment
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO fee_payments (student_id, fee_structure_id, amount_paid, 
                                        payment_method, receipt_number, remarks, recorded_by)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (student[0], fee_structure[0], amount_paid, payment_method, 
                  receipt_number, remarks, self.main_app.current_user['id']))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("Success", "Payment recorded successfully!")
            
            # Clear form
            self.student_var.set("")
            self.fee_structure_var.set("")
            for entry in self.payment_entries.values():
                if hasattr(entry, 'delete'):
                    entry.delete(0, tk.END)
                else:
                    entry.set("")
            
            # Refresh list
            self.refresh_payments()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to record payment: {str(e)}")
    
    def refresh_payments(self):
        """Refresh payments list"""
        # Clear existing items
        for item in self.payments_tree.get_children():
            self.payments_tree.delete(item)
        
        # Load payments
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT fp.id, u.full_name, fs.fee_type, fp.amount_paid, fp.payment_date,
                   fp.payment_method, fp.receipt_number, u2.full_name
            FROM fee_payments fp
            JOIN students s ON fp.student_id = s.id
            JOIN users u ON s.user_id = u.id
            JOIN fee_structure fs ON fp.fee_structure_id = fs.id
            LEFT JOIN users u2 ON fp.recorded_by = u2.id
            ORDER BY fp.payment_date DESC
        ''')
        
        payments = cursor.fetchall()
        conn.close()
        
        for payment in payments:
            self.payments_tree.insert('', 'end', values=payment)
    
    def setup_reports_tab(self):
        """Setup financial reports tab"""
        # Report controls
        controls_frame = ttk.LabelFrame(self.reports_tab, text="Generate Reports", padding=20)
        controls_frame.pack(fill='x', padx=10, pady=10)
        
        # Report type selection
        ttk.Label(controls_frame, text="Report Type:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.report_type_var = tk.StringVar(value="Monthly Collection")
        report_combo = ttk.Combobox(controls_frame, textvariable=self.report_type_var,
                                  values=["Monthly Collection", "Class-wise Collection", "Outstanding Fees", 
                                         "Payment History"], state="readonly", width=25)
        report_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # Generate button
        generate_btn = ttk.Button(controls_frame, text="Generate Report", command=self.generate_report)
        generate_btn.grid(row=0, column=2, padx=20, pady=5)
        
        # Report display area
        report_frame = ttk.Frame(self.reports_tab)
        report_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Text widget for report
        self.report_text = tk.Text(report_frame, wrap=tk.WORD, font=('Courier', 10))
        report_scrollbar = ttk.Scrollbar(report_frame, orient='vertical', command=self.report_text.yview)
        self.report_text.configure(yscrollcommand=report_scrollbar.set)
        
        self.report_text.pack(side='left', fill='both', expand=True)
        report_scrollbar.pack(side='right', fill='y')
    
    def generate_report(self):
        """Generate selected financial report"""
        report_type = self.report_type_var.get()
        
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, f"Generating {report_type} Report...\n\n")
        
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        if report_type == "Monthly Collection":
            cursor.execute('''
                SELECT strftime('%Y-%m', payment_date) as month, 
                       SUM(amount_paid) as total_collection,
                       COUNT(*) as total_payments
                FROM fee_payments
                GROUP BY strftime('%Y-%m', payment_date)
                ORDER BY month DESC
                LIMIT 12
            ''')
            
            results = cursor.fetchall()
            self.report_text.insert(tk.END, "Monthly Collection Report\n")
            self.report_text.insert(tk.END, "=" * 50 + "\n\n")
            self.report_text.insert(tk.END, f"{'Month':<15} {'Collection':<15} {'Payments':<10}\n")
            self.report_text.insert(tk.END, "-" * 40 + "\n")
            
            for result in results:
                self.report_text.insert(tk.END, f"{result[0]:<15} ${result[1]:<14.2f} {result[2]:<10}\n")
        
        elif report_type == "Outstanding Fees":
            # This is a simplified version - in reality, you'd need more complex logic
            self.report_text.insert(tk.END, "Outstanding Fees Report\n")
            self.report_text.insert(tk.END, "=" * 50 + "\n\n")
            self.report_text.insert(tk.END, "This report would show students with pending fee payments.\n")
            self.report_text.insert(tk.END, "Implementation requires complex calculation logic.\n")
        
        conn.close()
    
    def setup_reminders_tab(self):
        """Setup payment reminders tab"""
        # Filter frame
        filter_frame = ttk.LabelFrame(self.reminders_tab, text="Filter Pending Payments", padding=10)
        filter_frame.pack(fill='x', padx=10, pady=10)

        # Class filter
        ttk.Label(filter_frame, text="Class:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.pending_class_var = tk.StringVar(value="All Classes")
        class_combo = ttk.Combobox(filter_frame, textvariable=self.pending_class_var, width=15)
        class_combo.grid(row=0, column=1, padx=5, pady=5)

        # Amount threshold
        ttk.Label(filter_frame, text="Min Pending Amount:").grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.min_amount_var = tk.StringVar(value="0")
        amount_entry = ttk.Entry(filter_frame, textvariable=self.min_amount_var, width=10)
        amount_entry.grid(row=0, column=3, padx=5, pady=5)

        # Filter button
        filter_btn = ttk.Button(filter_frame, text="Show Pending Payments",
                               command=self.show_pending_payments)
        filter_btn.grid(row=0, column=4, padx=20, pady=5)

        # Pending payments list
        list_frame = ttk.LabelFrame(self.reminders_tab, text="Students with Pending Payments", padding=10)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Treeview for pending payments
        columns = ("Student", "Class", "Fee Type", "Total Due", "Paid", "Pending", "Days Overdue")
        self.pending_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)

        for col in columns:
            self.pending_tree.heading(col, text=col)
            self.pending_tree.column(col, width=100)

        # Scrollbar
        pending_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.pending_tree.yview)
        self.pending_tree.configure(yscrollcommand=pending_scrollbar.set)

        self.pending_tree.pack(side='left', fill='both', expand=True)
        pending_scrollbar.pack(side='right', fill='y')

        # Action buttons
        action_frame = ttk.Frame(self.reminders_tab)
        action_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(action_frame, text="Send Reminder", command=self.send_reminder).pack(side='left', padx=5)
        ttk.Button(action_frame, text="Record Payment", command=self.quick_payment).pack(side='left', padx=5)
        ttk.Button(action_frame, text="Export Pending List", command=self.export_pending).pack(side='left', padx=5)

        # Load class options and show pending payments
        self.load_class_options_pending(class_combo)
        self.show_pending_payments()

    def setup_outstanding_tab(self):
        """Setup outstanding amounts detailed view"""
        # Summary frame
        summary_frame = ttk.LabelFrame(self.outstanding_tab, text="Outstanding Summary", padding=10)
        summary_frame.pack(fill='x', padx=10, pady=10)

        # Summary labels
        self.total_outstanding_label = ttk.Label(summary_frame, text="Total Outstanding: $0.00",
                                               font=('Arial', 12, 'bold'), foreground='red')
        self.total_outstanding_label.pack(side='left', padx=20)

        self.students_with_dues_label = ttk.Label(summary_frame, text="Students with Dues: 0",
                                                font=('Arial', 12, 'bold'))
        self.students_with_dues_label.pack(side='left', padx=20)

        # Refresh button
        ttk.Button(summary_frame, text="Refresh", command=self.refresh_outstanding).pack(side='right', padx=10)

        # Detailed outstanding list
        detail_frame = ttk.LabelFrame(self.outstanding_tab, text="Detailed Outstanding Amounts", padding=10)
        detail_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Treeview for detailed outstanding
        columns = ("Student ID", "Student Name", "Class", "Fee Type", "Total Due", "Total Paid", "Outstanding", "Last Payment")
        self.outstanding_tree = ttk.Treeview(detail_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.outstanding_tree.heading(col, text=col)
            self.outstanding_tree.column(col, width=100)

        # Scrollbar
        outstanding_scrollbar = ttk.Scrollbar(detail_frame, orient='vertical', command=self.outstanding_tree.yview)
        self.outstanding_tree.configure(yscrollcommand=outstanding_scrollbar.set)

        self.outstanding_tree.pack(side='left', fill='both', expand=True)
        outstanding_scrollbar.pack(side='right', fill='y')

        # Load outstanding data
        self.refresh_outstanding()
    
    def show_my_fees(self):
        """Show fee status for current student"""
        self.main_app.clear_content()

        title_label = ttk.Label(self.main_app.content_frame, text="My Fee Status", style='Title.TLabel')
        title_label.pack(pady=10)

        # Get current student data
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT s.id, s.student_id, s.class_name, u.full_name
            FROM students s
            JOIN users u ON s.user_id = u.id
            WHERE u.id = ?
        ''', (self.main_app.current_user['id'],))

        student_data = cursor.fetchone()

        if not student_data:
            error_label = ttk.Label(self.main_app.content_frame,
                                  text="Student profile not found. Please contact administrator.")
            error_label.pack(pady=20)
            conn.close()
            return

        # Student info
        info_frame = ttk.LabelFrame(self.main_app.content_frame, text="Student Information", padding=10)
        info_frame.pack(fill='x', padx=10, pady=10)

        ttk.Label(info_frame, text=f"Name: {student_data[3]}", font=('Arial', 10)).pack(anchor='w')
        ttk.Label(info_frame, text=f"Student ID: {student_data[1]}", font=('Arial', 10)).pack(anchor='w')
        ttk.Label(info_frame, text=f"Class: {student_data[2]}", font=('Arial', 10)).pack(anchor='w')

        # Fee structure for this class
        cursor.execute('''
            SELECT id, fee_type, amount, due_date_day, academic_year
            FROM fee_structure
            WHERE class_name = ? AND is_active = 1
            ORDER BY fee_type
        ''', (student_data[2],))

        fee_structures = cursor.fetchall()

        if not fee_structures:
            no_fees_label = ttk.Label(self.main_app.content_frame,
                                    text="No fee structure found for your class.")
            no_fees_label.pack(pady=20)
            conn.close()
            return

        # Fee status frame
        status_frame = ttk.LabelFrame(self.main_app.content_frame, text="Fee Status", padding=10)
        status_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Fee status table
        columns = ("Fee Type", "Amount Due", "Amount Paid", "Balance", "Status")
        fee_tree = ttk.Treeview(status_frame, columns=columns, show='headings', height=8)

        for col in columns:
            fee_tree.heading(col, text=col)
            fee_tree.column(col, width=120)

        # Scrollbar
        scrollbar = ttk.Scrollbar(status_frame, orient='vertical', command=fee_tree.yview)
        fee_tree.configure(yscrollcommand=scrollbar.set)

        fee_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Calculate fee status
        total_due = 0
        total_paid = 0

        for fee_structure in fee_structures:
            # Get total payments for this fee type
            cursor.execute('''
                SELECT COALESCE(SUM(amount_paid), 0)
                FROM fee_payments
                WHERE student_id = ? AND fee_structure_id = ?
            ''', (student_data[0], fee_structure[0]))

            amount_paid = cursor.fetchone()[0]
            amount_due = fee_structure[2]
            balance = amount_due - amount_paid

            status = "Paid" if balance <= 0 else "Pending" if balance < amount_due else "Not Paid"

            fee_tree.insert('', 'end', values=(
                fee_structure[1],  # fee_type
                f"${amount_due:.2f}",
                f"${amount_paid:.2f}",
                f"${balance:.2f}",
                status
            ))

            total_due += amount_due
            total_paid += amount_paid

        # Summary frame
        summary_frame = ttk.Frame(self.main_app.content_frame)
        summary_frame.pack(fill='x', padx=10, pady=10)

        total_balance = total_due - total_paid

        ttk.Label(summary_frame, text=f"Total Due: ${total_due:.2f}",
                 font=('Arial', 12, 'bold')).pack(side='left', padx=20)
        ttk.Label(summary_frame, text=f"Total Paid: ${total_paid:.2f}",
                 font=('Arial', 12, 'bold')).pack(side='left', padx=20)
        ttk.Label(summary_frame, text=f"Outstanding Balance: ${total_balance:.2f}",
                 font=('Arial', 12, 'bold'),
                 foreground='red' if total_balance > 0 else 'green').pack(side='left', padx=20)

        # Payment history
        history_frame = ttk.LabelFrame(self.main_app.content_frame, text="Payment History", padding=10)
        history_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Payment history table
        history_columns = ("Date", "Fee Type", "Amount", "Method", "Receipt No")
        history_tree = ttk.Treeview(history_frame, columns=history_columns, show='headings', height=6)

        for col in history_columns:
            history_tree.heading(col, text=col)
            history_tree.column(col, width=120)

        # Scrollbar for history
        history_scrollbar = ttk.Scrollbar(history_frame, orient='vertical', command=history_tree.yview)
        history_tree.configure(yscrollcommand=history_scrollbar.set)

        history_tree.pack(side='left', fill='both', expand=True)
        history_scrollbar.pack(side='right', fill='y')

        # Load payment history
        cursor.execute('''
            SELECT fp.payment_date, fs.fee_type, fp.amount_paid,
                   fp.payment_method, fp.receipt_number
            FROM fee_payments fp
            JOIN fee_structure fs ON fp.fee_structure_id = fs.id
            WHERE fp.student_id = ?
            ORDER BY fp.payment_date DESC
        ''', (student_data[0],))

        payment_history = cursor.fetchall()

        for payment in payment_history:
            history_tree.insert('', 'end', values=(
                payment[0],  # date
                payment[1],  # fee_type
                f"${payment[2]:.2f}",  # amount
                payment[3],  # method
                payment[4] or "N/A"  # receipt
            ))

        conn.close()

    def load_class_options_pending(self, combo_widget):
        """Load class options for pending payments filter"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT DISTINCT class_name FROM students ORDER BY class_name")
        classes = [row[0] for row in cursor.fetchall()]
        classes.insert(0, "All Classes")

        combo_widget['values'] = classes
        conn.close()

    def show_pending_payments(self):
        """Show students with pending payments"""
        # Clear existing items
        for item in self.pending_tree.get_children():
            self.pending_tree.delete(item)

        class_filter = self.pending_class_var.get()
        min_amount = float(self.min_amount_var.get() or 0)

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # Build query for pending payments
        query = '''
            SELECT s.student_id, u.full_name, s.class_name, fs.fee_type,
                   fs.amount as total_due,
                   COALESCE(SUM(fp.amount_paid), 0) as total_paid,
                   (fs.amount - COALESCE(SUM(fp.amount_paid), 0)) as pending,
                   MAX(fp.payment_date) as last_payment_date
            FROM students s
            JOIN users u ON s.user_id = u.id
            JOIN fee_structure fs ON s.class_name = fs.class_name
            LEFT JOIN fee_payments fp ON s.id = fp.student_id AND fs.id = fp.fee_structure_id
            WHERE fs.is_active = 1 AND u.is_active = 1
        '''

        params = []

        # Apply class filter
        if class_filter and class_filter != "All Classes":
            query += " AND s.class_name = ?"
            params.append(class_filter)

        query += '''
            GROUP BY s.id, fs.id
            HAVING (fs.amount - COALESCE(SUM(fp.amount_paid), 0)) > ?
            ORDER BY pending DESC, u.full_name
        '''
        params.append(min_amount)

        cursor.execute(query, params)
        pending_data = cursor.fetchall()

        # Calculate days overdue (simplified - assuming due date is 10th of each month)
        from datetime import datetime, date
        today = date.today()

        for data in pending_data:
            student_id = data[0]
            student_name = data[1]
            class_name = data[2]
            fee_type = data[3]
            total_due = data[4]
            total_paid = data[5]
            pending = data[6]
            last_payment = data[7]

            # Calculate days overdue (simplified calculation)
            if today.day > 10:  # Assuming due date is 10th of each month
                days_overdue = today.day - 10
            else:
                days_overdue = 0

            self.pending_tree.insert('', 'end', values=(
                student_name, class_name, fee_type,
                f"${total_due:.2f}", f"${total_paid:.2f}", f"${pending:.2f}",
                f"{days_overdue} days" if days_overdue > 0 else "Not Due"
            ))

        conn.close()

    def refresh_outstanding(self):
        """Refresh outstanding amounts display"""
        # Clear existing items
        for item in self.outstanding_tree.get_children():
            self.outstanding_tree.delete(item)

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # Get detailed outstanding amounts
        cursor.execute('''
            SELECT s.student_id, u.full_name, s.class_name, fs.fee_type,
                   fs.amount as total_due,
                   COALESCE(SUM(fp.amount_paid), 0) as total_paid,
                   (fs.amount - COALESCE(SUM(fp.amount_paid), 0)) as outstanding,
                   MAX(fp.payment_date) as last_payment_date
            FROM students s
            JOIN users u ON s.user_id = u.id
            JOIN fee_structure fs ON s.class_name = fs.class_name
            LEFT JOIN fee_payments fp ON s.id = fp.student_id AND fs.id = fp.fee_structure_id
            WHERE fs.is_active = 1 AND u.is_active = 1
            GROUP BY s.id, fs.id
            HAVING (fs.amount - COALESCE(SUM(fp.amount_paid), 0)) > 0
            ORDER BY outstanding DESC, u.full_name
        ''')

        outstanding_data = cursor.fetchall()

        total_outstanding = 0
        students_with_dues = set()

        for data in outstanding_data:
            student_id = data[0]
            student_name = data[1]
            class_name = data[2]
            fee_type = data[3]
            total_due = data[4]
            total_paid = data[5]
            outstanding = data[6]
            last_payment = data[7] or "No Payment"

            total_outstanding += outstanding
            students_with_dues.add(student_id)

            self.outstanding_tree.insert('', 'end', values=(
                student_id, student_name, class_name, fee_type,
                f"${total_due:.2f}", f"${total_paid:.2f}", f"${outstanding:.2f}",
                last_payment
            ))

        # Update summary labels
        self.total_outstanding_label.config(text=f"Total Outstanding: ${total_outstanding:.2f}")
        self.students_with_dues_label.config(text=f"Students with Dues: {len(students_with_dues)}")

        conn.close()

    def send_reminder(self):
        """Send payment reminder to selected student"""
        selection = self.pending_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a student to send reminder")
            return

        item = self.pending_tree.item(selection[0])
        student_name = item['values'][0]
        pending_amount = item['values'][5]

        # In a real system, this would send email/SMS
        messagebox.showinfo("Reminder Sent",
                           f"Payment reminder sent to {student_name}\n" +
                           f"Pending Amount: {pending_amount}\n\n" +
                           "Note: In a real system, this would send an actual email/SMS reminder.")

    def quick_payment(self):
        """Quick payment entry for selected student"""
        selection = self.pending_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a student to record payment")
            return

        item = self.pending_tree.item(selection[0])
        student_name = item['values'][0]
        pending_amount = item['values'][5].replace('$', '')

        # Create quick payment dialog
        self.create_quick_payment_dialog(student_name, pending_amount)

    def export_pending(self):
        """Export pending payments list"""
        try:
            from datetime import datetime

            # Get all pending data
            pending_data = []
            for item in self.pending_tree.get_children():
                values = self.pending_tree.item(item)['values']
                pending_data.append(values)

            if not pending_data:
                messagebox.showwarning("Warning", "No pending payments to export")
                return

            # Create export content
            export_content = "PENDING PAYMENTS REPORT\n"
            export_content += "=" * 80 + "\n"
            export_content += f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            export_content += f"Total Records: {len(pending_data)}\n\n"

            export_content += f"{'Student':<20} {'Class':<10} {'Fee Type':<15} {'Due':<10} {'Paid':<10} {'Pending':<10} {'Status':<15}\n"
            export_content += "-" * 100 + "\n"

            total_pending = 0
            for data in pending_data:
                export_content += f"{data[0]:<20} {data[1]:<10} {data[2]:<15} {data[3]:<10} {data[4]:<10} {data[5]:<10} {data[6]:<15}\n"
                # Extract pending amount for total
                pending_str = data[5].replace('$', '')
                try:
                    total_pending += float(pending_str)
                except:
                    pass

            export_content += "-" * 100 + "\n"
            export_content += f"TOTAL PENDING AMOUNT: ${total_pending:.2f}\n"

            # Save to file
            filename = f"pending_payments_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(export_content)

            messagebox.showinfo("Export Complete", f"Pending payments exported to {filename}")

        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export pending payments: {str(e)}")

    def create_quick_payment_dialog(self, student_name, pending_amount):
        """Create quick payment entry dialog"""
        dialog = tk.Toplevel(self.main_app.root)
        dialog.title(f"Record Payment - {student_name}")
        dialog.geometry("400x300")
        dialog.transient(self.main_app.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - 200
        y = (dialog.winfo_screenheight() // 2) - 150
        dialog.geometry(f"400x300+{x}+{y}")

        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        ttk.Label(main_frame, text=f"Record Payment for {student_name}",
                 font=('Arial', 12, 'bold')).pack(pady=10)

        ttk.Label(main_frame, text=f"Pending Amount: ${pending_amount}",
                 font=('Arial', 10), foreground='red').pack(pady=5)

        # Payment amount
        ttk.Label(main_frame, text="Payment Amount:").pack(pady=5)
        amount_var = tk.StringVar(value=pending_amount)
        amount_entry = ttk.Entry(main_frame, textvariable=amount_var, width=20)
        amount_entry.pack(pady=5)

        # Payment method
        ttk.Label(main_frame, text="Payment Method:").pack(pady=5)
        method_var = tk.StringVar(value="Cash")
        method_combo = ttk.Combobox(main_frame, textvariable=method_var,
                                   values=["Cash", "Bank Transfer", "Cheque", "Online"],
                                   state="readonly", width=17)
        method_combo.pack(pady=5)

        # Receipt number
        ttk.Label(main_frame, text="Receipt Number (Optional):").pack(pady=5)
        receipt_var = tk.StringVar()
        receipt_entry = ttk.Entry(main_frame, textvariable=receipt_var, width=20)
        receipt_entry.pack(pady=5)

        def save_payment():
            try:
                amount = float(amount_var.get())
                if amount <= 0:
                    messagebox.showerror("Error", "Payment amount must be greater than 0")
                    return

                # Find student and record payment
                conn = db_manager.get_connection()
                cursor = conn.cursor()

                # Get student ID by name
                cursor.execute("SELECT s.id FROM students s JOIN users u ON s.user_id = u.id WHERE u.full_name = ?",
                              (student_name,))
                student_result = cursor.fetchone()

                if not student_result:
                    messagebox.showerror("Error", "Student not found")
                    conn.close()
                    return

                student_id = student_result[0]

                # Get the first active fee structure for this student's class
                cursor.execute('''
                    SELECT fs.id FROM fee_structure fs
                    JOIN students s ON fs.class_name = s.class_name
                    WHERE s.id = ? AND fs.is_active = 1
                    LIMIT 1
                ''', (student_id,))

                fee_result = cursor.fetchone()
                if not fee_result:
                    messagebox.showerror("Error", "No active fee structure found")
                    conn.close()
                    return

                fee_structure_id = fee_result[0]

                # Record payment
                cursor.execute('''
                    INSERT INTO fee_payments (student_id, fee_structure_id, amount_paid,
                                            payment_method, receipt_number, recorded_by)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (student_id, fee_structure_id, amount, method_var.get(),
                      receipt_var.get() or None, self.main_app.current_user['id']))

                conn.commit()
                conn.close()

                messagebox.showinfo("Success",
                                   f"Payment of ${amount:.2f} recorded successfully!\n" +
                                   f"Method: {method_var.get()}\n" +
                                   f"Receipt: {receipt_var.get() or 'N/A'}")
                dialog.destroy()
                self.show_pending_payments()
                self.refresh_outstanding()

            except ValueError:
                messagebox.showerror("Error", "Please enter a valid payment amount")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to record payment: {str(e)}")

        # Buttons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=20)

        ttk.Button(btn_frame, text="Record Payment", command=save_payment).pack(side='left', padx=10)
        ttk.Button(btn_frame, text="Cancel", command=dialog.destroy).pack(side='left', padx=10)
