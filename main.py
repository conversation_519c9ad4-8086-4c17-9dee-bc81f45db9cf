import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Import modules
from database import db_manager
from user_management import UserManagement
from result_management import ResultManagement
from financial_management import FinancialManagement

class StudentManagementSystem:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Student Result Management System")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # Center the window
        self.center_window()
        
        # Current user
        self.current_user = None
        
        # Style configuration
        self.setup_styles()
        
        # Initialize modules
        self.user_mgmt = UserManagement(self)
        self.result_mgmt = ResultManagement(self)
        self.financial_mgmt = FinancialManagement(self)
        
        # Show login screen
        self.show_login()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_styles(self):
        """Setup modern professional styles for the application"""
        style = ttk.Style()
        style.theme_use('clam')

        # Modern Color Palette
        self.colors = {
            'primary': '#2C3E50',      # Dark Blue-Gray
            'secondary': '#3498DB',     # Bright Blue
            'accent': '#E74C3C',        # Red
            'success': '#27AE60',       # Green
            'warning': '#F39C12',       # Orange
            'light': '#ECF0F1',         # Light Gray
            'white': '#FFFFFF',         # White
            'dark': '#34495E',          # Dark Gray
            'gradient_start': '#667eea', # Gradient Blue
            'gradient_end': '#764ba2',   # Gradient Purple
            'sidebar': '#2C3E50',       # Sidebar Dark
            'hover': '#3498DB'          # Hover Blue
        }

        # Configure modern styles
        style.configure('Title.TLabel',
                       font=('Segoe UI', 24, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['white'])

        style.configure('Subtitle.TLabel',
                       font=('Segoe UI', 14),
                       foreground=self.colors['dark'],
                       background=self.colors['white'])

        style.configure('Heading.TLabel',
                       font=('Segoe UI', 16, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['white'])

        style.configure('Modern.TButton',
                       font=('Segoe UI', 11, 'bold'),
                       foreground=self.colors['white'],
                       background=self.colors['secondary'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(20, 10))

        style.map('Modern.TButton',
                 background=[('active', self.colors['hover']),
                           ('pressed', self.colors['primary'])])

        style.configure('Primary.TButton',
                       font=('Segoe UI', 12, 'bold'),
                       foreground=self.colors['white'],
                       background=self.colors['primary'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(25, 12))

        style.map('Primary.TButton',
                 background=[('active', self.colors['dark']),
                           ('pressed', self.colors['secondary'])])

        style.configure('Success.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       foreground=self.colors['white'],
                       background=self.colors['success'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(15, 8))

        style.configure('Warning.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       foreground=self.colors['white'],
                       background=self.colors['warning'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(15, 8))

        style.configure('Danger.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       foreground=self.colors['white'],
                       background=self.colors['accent'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(15, 8))

        style.configure('Modern.TEntry',
                       font=('Segoe UI', 11),
                       borderwidth=2,
                       relief='solid',
                       bordercolor=self.colors['light'],
                       lightcolor=self.colors['light'],
                       darkcolor=self.colors['light'],
                       focuscolor=self.colors['secondary'],
                       padding=(10, 8))

        style.configure('Modern.TFrame',
                       background=self.colors['white'],
                       relief='flat',
                       borderwidth=0)

        style.configure('Card.TFrame',
                       background=self.colors['white'],
                       relief='solid',
                       borderwidth=1,
                       bordercolor=self.colors['light'])

        style.configure('Sidebar.TFrame',
                       background=self.colors['sidebar'])

        style.configure('Modern.TLabelFrame',
                       background=self.colors['white'],
                       borderwidth=2,
                       relief='solid',
                       bordercolor=self.colors['light'])

        style.configure('Modern.TLabelFrame.Label',
                       font=('Segoe UI', 12, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['white'])

        # Notebook styles
        style.configure('Modern.TNotebook',
                       background=self.colors['white'],
                       borderwidth=0)

        style.configure('Modern.TNotebook.Tab',
                       font=('Segoe UI', 11, 'bold'),
                       foreground=self.colors['dark'],
                       background=self.colors['light'],
                       padding=(20, 10),
                       borderwidth=0)

        style.map('Modern.TNotebook.Tab',
                 background=[('selected', self.colors['secondary']),
                           ('active', self.colors['hover'])],
                 foreground=[('selected', self.colors['white']),
                           ('active', self.colors['white'])])

        # Treeview styles
        style.configure('Modern.Treeview',
                       font=('Segoe UI', 10),
                       background=self.colors['white'],
                       foreground=self.colors['dark'],
                       borderwidth=1,
                       relief='solid')

        style.configure('Modern.Treeview.Heading',
                       font=('Segoe UI', 11, 'bold'),
                       foreground=self.colors['white'],
                       background=self.colors['primary'],
                       borderwidth=0,
                       relief='flat')

        style.map('Modern.Treeview',
                 background=[('selected', self.colors['secondary'])])

        # Configure root window
        self.root.configure(bg=self.colors['light'])
        
    def clear_window(self):
        """Clear all widgets from the window"""
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def show_login(self):
        """Show modern professional login interface"""
        self.clear_window()

        # Create gradient background effect
        self.root.configure(bg=self.colors['light'])

        # Main container with centered layout
        main_container = tk.Frame(self.root, bg=self.colors['light'])
        main_container.pack(expand=True, fill='both')

        # Login card container
        login_container = tk.Frame(main_container, bg=self.colors['light'])
        login_container.pack(expand=True)

        # Login card with shadow effect
        login_card = tk.Frame(login_container,
                             bg=self.colors['white'],
                             relief='solid',
                             borderwidth=1,
                             bd=0)
        login_card.pack(pady=80, padx=50, ipadx=40, ipady=40)

        # Add subtle shadow effect
        shadow_frame = tk.Frame(login_container,
                               bg='#BDC3C7',
                               height=2)
        shadow_frame.place(in_=login_card, x=3, y=3, relwidth=1, relheight=1)
        login_card.lift()

        # Header section with logo area
        header_frame = tk.Frame(login_card, bg=self.colors['white'])
        header_frame.pack(fill='x', pady=(0, 30))

        # Logo placeholder
        logo_frame = tk.Frame(header_frame,
                             bg=self.colors['secondary'],
                             width=80,
                             height=80)
        logo_frame.pack(pady=(0, 20))
        logo_frame.pack_propagate(False)

        # Logo text
        logo_label = tk.Label(logo_frame,
                             text="SMS",
                             font=('Segoe UI', 24, 'bold'),
                             fg=self.colors['white'],
                             bg=self.colors['secondary'])
        logo_label.pack(expand=True)

        # System title
        title_label = tk.Label(header_frame,
                              text="Student Management System",
                              font=('Segoe UI', 28, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['white'])
        title_label.pack()

        # Subtitle
        subtitle_label = tk.Label(header_frame,
                                 text="Professional Academic Management Solution",
                                 font=('Segoe UI', 12),
                                 fg=self.colors['dark'],
                                 bg=self.colors['white'])
        subtitle_label.pack(pady=(5, 0))

        # Login form section
        form_frame = tk.Frame(login_card, bg=self.colors['white'])
        form_frame.pack(fill='x', pady=20)

        # Welcome message
        welcome_label = tk.Label(form_frame,
                                text="Welcome Back!",
                                font=('Segoe UI', 18, 'bold'),
                                fg=self.colors['primary'],
                                bg=self.colors['white'])
        welcome_label.pack(pady=(0, 20))

        # Username field
        username_label = tk.Label(form_frame,
                                 text="Username",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg=self.colors['dark'],
                                 bg=self.colors['white'])
        username_label.pack(anchor='w', padx=20, pady=(10, 5))

        self.username_entry = tk.Entry(form_frame,
                                      font=('Segoe UI', 14),
                                      width=25,
                                      relief='solid',
                                      borderwidth=2,
                                      bd=1,
                                      highlightthickness=0,
                                      bg=self.colors['white'],
                                      fg=self.colors['dark'],
                                      insertbackground=self.colors['secondary'])
        self.username_entry.pack(padx=20, pady=(0, 15), ipady=8)

        # Password field
        password_label = tk.Label(form_frame,
                                 text="Password",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg=self.colors['dark'],
                                 bg=self.colors['white'])
        password_label.pack(anchor='w', padx=20, pady=(0, 5))

        self.password_entry = tk.Entry(form_frame,
                                      font=('Segoe UI', 14),
                                      width=25,
                                      show='*',
                                      relief='solid',
                                      borderwidth=2,
                                      bd=1,
                                      highlightthickness=0,
                                      bg=self.colors['white'],
                                      fg=self.colors['dark'],
                                      insertbackground=self.colors['secondary'])
        self.password_entry.pack(padx=20, pady=(0, 25), ipady=8)

        # Login button with modern styling
        login_btn = tk.Button(form_frame,
                             text="LOGIN",
                             command=self.login,
                             font=('Segoe UI', 14, 'bold'),
                             fg=self.colors['white'],
                             bg=self.colors['secondary'],
                             activeforeground=self.colors['white'],
                             activebackground=self.colors['hover'],
                             relief='flat',
                             borderwidth=0,
                             cursor='hand2',
                             width=20,
                             pady=12)
        login_btn.pack(padx=20, pady=(0, 20))

        # Add hover effects
        def on_enter(e):
            login_btn.config(bg=self.colors['hover'])

        def on_leave(e):
            login_btn.config(bg=self.colors['secondary'])

        login_btn.bind("<Enter>", on_enter)
        login_btn.bind("<Leave>", on_leave)

        # Footer with demo credentials
        footer_frame = tk.Frame(login_card, bg=self.colors['light'], relief='solid', borderwidth=1)
        footer_frame.pack(fill='x', pady=(20, 0))

        demo_label = tk.Label(footer_frame,
                             text="Demo Credentials",
                             font=('Segoe UI', 10, 'bold'),
                             fg=self.colors['primary'],
                             bg=self.colors['light'])
        demo_label.pack(pady=(10, 5))

        credentials_text = "Admin: admin / admin123\nTeacher: testteacher / teacher123\nStudent: teststudent / student123"
        credentials_label = tk.Label(footer_frame,
                                   text=credentials_text,
                                   font=('Segoe UI', 9),
                                   fg=self.colors['dark'],
                                   bg=self.colors['light'],
                                   justify='left')
        credentials_label.pack(pady=(0, 10))

        # Bind Enter key to login
        self.root.bind('<Return>', lambda event: self.login())

        # Focus on username entry
        self.username_entry.focus()

        # Add focus effects to entry fields
        def on_entry_focus_in(event):
            event.widget.config(borderwidth=2, relief='solid')

        def on_entry_focus_out(event):
            event.widget.config(borderwidth=1, relief='solid')

        self.username_entry.bind("<FocusIn>", on_entry_focus_in)
        self.username_entry.bind("<FocusOut>", on_entry_focus_out)
        self.password_entry.bind("<FocusIn>", on_entry_focus_in)
        self.password_entry.bind("<FocusOut>", on_entry_focus_out)
        self.username_entry.focus()
    
    def login(self):
        """Handle user login"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("Error", "Please enter both username and password")
            return
        
        user = db_manager.authenticate_user(username, password)
        if user:
            self.current_user = user
            self.show_dashboard()
        else:
            messagebox.showerror("Error", "Invalid username or password")
            self.password_entry.delete(0, tk.END)
    
    def logout(self):
        """Handle user logout"""
        self.current_user = None
        self.show_login()
    
    def show_dashboard(self):
        """Show modern professional dashboard based on user role"""
        self.clear_window()

        # Configure root window
        self.root.configure(bg=self.colors['light'])

        # Create main layout with sidebar
        self.create_modern_dashboard_layout()

        # Show home content
        self.show_home()

    def create_modern_dashboard_layout(self):
        """Create modern dashboard layout with sidebar navigation"""
        # Main container
        main_container = tk.Frame(self.root, bg=self.colors['light'])
        main_container.pack(fill='both', expand=True)

        # Top header bar
        self.create_header_bar(main_container)

        # Content area with sidebar
        content_container = tk.Frame(main_container, bg=self.colors['light'])
        content_container.pack(fill='both', expand=True)

        # Sidebar navigation
        self.create_sidebar(content_container)

        # Main content area
        self.create_content_area(content_container)

    def create_header_bar(self, parent):
        """Create modern header bar"""
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=70)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Left side - Logo and title
        left_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        left_frame.pack(side='left', fill='y', padx=20, pady=10)

        # Logo
        logo_label = tk.Label(left_frame,
                             text="SMS",
                             font=('Segoe UI', 18, 'bold'),
                             fg=self.colors['white'],
                             bg=self.colors['primary'])
        logo_label.pack(side='left', padx=(0, 15))

        # System title
        title_label = tk.Label(left_frame,
                              text="Student Management System",
                              font=('Segoe UI', 16, 'bold'),
                              fg=self.colors['white'],
                              bg=self.colors['primary'])
        title_label.pack(side='left')

        # Right side - User info and logout
        right_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        right_frame.pack(side='right', fill='y', padx=20, pady=10)

        # User info
        user_info = f"{self.current_user['full_name']} ({self.current_user['role'].title()})"
        user_label = tk.Label(right_frame,
                             text=user_info,
                             font=('Segoe UI', 12),
                             fg=self.colors['white'],
                             bg=self.colors['primary'])
        user_label.pack(side='left', padx=(0, 20))

        # Logout button
        logout_btn = tk.Button(right_frame,
                              text="LOGOUT",
                              command=self.logout,
                              font=('Segoe UI', 10, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['white'],
                              activeforeground=self.colors['white'],
                              activebackground=self.colors['accent'],
                              relief='flat',
                              borderwidth=0,
                              cursor='hand2',
                              padx=20,
                              pady=8)
        logout_btn.pack(side='right')

        # Add hover effect to logout button
        def on_logout_enter(e):
            logout_btn.config(bg=self.colors['accent'], fg=self.colors['white'])

        def on_logout_leave(e):
            logout_btn.config(bg=self.colors['white'], fg=self.colors['primary'])

        logout_btn.bind("<Enter>", on_logout_enter)
        logout_btn.bind("<Leave>", on_logout_leave)

    def create_sidebar(self, parent):
        """Create modern sidebar navigation"""
        self.sidebar_frame = tk.Frame(parent, bg=self.colors['sidebar'], width=280)
        self.sidebar_frame.pack(side='left', fill='y')
        self.sidebar_frame.pack_propagate(False)

        # Sidebar header
        sidebar_header = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'], height=60)
        sidebar_header.pack(fill='x', pady=(20, 30))
        sidebar_header.pack_propagate(False)

        # Navigation title
        nav_title = tk.Label(sidebar_header,
                            text="NAVIGATION",
                            font=('Segoe UI', 12, 'bold'),
                            fg=self.colors['light'],
                            bg=self.colors['sidebar'])
        nav_title.pack(pady=15)

        # Navigation buttons container
        self.nav_container = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        self.nav_container.pack(fill='both', expand=True, padx=10)

        # Create navigation buttons based on role
        self.create_navigation_buttons()

    def create_content_area(self, parent):
        """Create main content area"""
        # Content frame with modern styling
        self.content_frame = tk.Frame(parent, bg=self.colors['white'])
        self.content_frame.pack(side='right', fill='both', expand=True, padx=(0, 0))
        
    def create_navigation_buttons(self):
        """Create modern navigation buttons based on user role"""
        # Define navigation items based on role
        nav_items = [("🏠", "Home", self.show_home)]

        if self.current_user['role'] == 'admin':
            nav_items.extend([
                ("👥", "User Management", self.user_mgmt.show_user_management),
                ("📊", "Result Management", self.result_mgmt.show_result_management),
                ("💰", "Financial Management", self.financial_mgmt.show_financial_management),
                ("📈", "Reports", self.show_reports)
            ])
        elif self.current_user['role'] == 'teacher':
            nav_items.extend([
                ("✏️", "Result Entry", self.result_mgmt.show_result_entry),
                ("👨‍🎓", "My Students", self.result_mgmt.show_my_students),
                ("📋", "My Reports", self.show_teacher_reports),
                ("📊", "Class Analysis", self.show_class_analysis)
            ])
        elif self.current_user['role'] == 'student':
            nav_items.extend([
                ("🎯", "My Results", self.result_mgmt.show_my_results),
                ("💳", "Fee Status", self.financial_mgmt.show_my_fees)
            ])

        # Create modern navigation buttons
        self.nav_buttons = []
        for icon, text, command in nav_items:
            btn_frame = tk.Frame(self.nav_container, bg=self.colors['sidebar'])
            btn_frame.pack(fill='x', pady=2)

            btn = tk.Button(btn_frame,
                           text=f"{icon}  {text}",
                           command=command,
                           font=('Segoe UI', 12),
                           fg=self.colors['light'],
                           bg=self.colors['sidebar'],
                           activeforeground=self.colors['white'],
                           activebackground=self.colors['hover'],
                           relief='flat',
                           borderwidth=0,
                           cursor='hand2',
                           anchor='w',
                           padx=20,
                           pady=12)
            btn.pack(fill='x')

            # Add hover effects
            def on_enter(e, button=btn):
                if button.cget('bg') != self.colors['secondary']:  # Don't change active button
                    button.config(bg=self.colors['hover'], fg=self.colors['white'])

            def on_leave(e, button=btn):
                if button.cget('bg') != self.colors['secondary']:  # Don't change active button
                    button.config(bg=self.colors['sidebar'], fg=self.colors['light'])

            def on_click(e, button=btn, cmd=command):
                # Reset all buttons
                for nav_btn in self.nav_buttons:
                    nav_btn.config(bg=self.colors['sidebar'], fg=self.colors['light'])
                # Highlight clicked button
                button.config(bg=self.colors['secondary'], fg=self.colors['white'])
                # Execute command
                cmd()

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)
            btn.bind("<Button-1>", on_click)

            self.nav_buttons.append(btn)

        # Highlight home button by default
        if self.nav_buttons:
            self.nav_buttons[0].config(bg=self.colors['secondary'], fg=self.colors['white'])
    
    def clear_content(self):
        """Clear content frame"""
        if hasattr(self, 'content_frame') and self.content_frame:
            for widget in self.content_frame.winfo_children():
                widget.destroy()

    def clear_window(self):
        """Clear entire window"""
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def show_home(self):
        """Show modern home dashboard"""
        self.clear_content()

        # Main dashboard container
        dashboard_container = tk.Frame(self.content_frame, bg=self.colors['white'])
        dashboard_container.pack(fill='both', expand=True, padx=30, pady=30)

        # Dashboard header
        header_frame = tk.Frame(dashboard_container, bg=self.colors['white'])
        header_frame.pack(fill='x', pady=(0, 30))

        # Welcome message
        welcome_label = tk.Label(header_frame,
                                text=f"Welcome back, {self.current_user['full_name']}!",
                                font=('Segoe UI', 24, 'bold'),
                                fg=self.colors['primary'],
                                bg=self.colors['white'])
        welcome_label.pack(anchor='w')

        # Role and date
        from datetime import datetime
        current_date = datetime.now().strftime("%A, %B %d, %Y")

        info_frame = tk.Frame(header_frame, bg=self.colors['white'])
        info_frame.pack(fill='x', pady=(10, 0))

        role_label = tk.Label(info_frame,
                             text=f"{self.current_user['role'].title()} Dashboard",
                             font=('Segoe UI', 14),
                             fg=self.colors['secondary'],
                             bg=self.colors['white'])
        role_label.pack(side='left')

        date_label = tk.Label(info_frame,
                             text=current_date,
                             font=('Segoe UI', 12),
                             fg=self.colors['dark'],
                             bg=self.colors['white'])
        date_label.pack(side='right')

        # Dashboard content based on role
        if self.current_user['role'] == 'admin':
            self.show_admin_dashboard(dashboard_container)
        elif self.current_user['role'] == 'teacher':
            self.show_teacher_dashboard(dashboard_container)
        elif self.current_user['role'] == 'student':
            self.show_student_dashboard(dashboard_container)
    
    def show_admin_dashboard(self, parent):
        """Show modern admin dashboard with statistics cards"""
        # Statistics cards container
        cards_container = tk.Frame(parent, bg=self.colors['white'])
        cards_container.pack(fill='x', pady=(0, 30))

        # Get statistics
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # Count statistics
        cursor.execute("SELECT COUNT(*) FROM students")
        total_students = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM teachers")
        total_teachers = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM subjects")
        total_subjects = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM results")
        total_results = cursor.fetchone()[0]

        # Get financial statistics
        cursor.execute('''
            SELECT COALESCE(SUM(fs.amount - COALESCE(fp.amount_paid, 0)), 0)
            FROM fee_structure fs
            LEFT JOIN (
                SELECT fee_structure_id, SUM(amount_paid) as amount_paid
                FROM fee_payments
                GROUP BY fee_structure_id
            ) fp ON fs.id = fp.fee_structure_id
            WHERE fs.is_active = 1
        ''')
        outstanding_fees = cursor.fetchone()[0] or 0

        conn.close()

        # Create modern statistics cards
        stats_data = [
            ("👨‍🎓", "Students", total_students, self.colors['secondary']),
            ("👨‍🏫", "Teachers", total_teachers, self.colors['success']),
            ("📚", "Subjects", total_subjects, self.colors['warning']),
            ("💰", "Outstanding", f"${outstanding_fees:.0f}", self.colors['accent'])
        ]

        # Create cards in a grid
        for i, (icon, title, value, color) in enumerate(stats_data):
            self.create_stat_card(cards_container, icon, title, value, color, i)

        # Recent activity section
        activity_frame = tk.Frame(parent, bg=self.colors['white'])
        activity_frame.pack(fill='both', expand=True, pady=(20, 0))

        # Activity header
        activity_header = tk.Label(activity_frame,
                                  text="Quick Actions",
                                  font=('Segoe UI', 18, 'bold'),
                                  fg=self.colors['primary'],
                                  bg=self.colors['white'])
        activity_header.pack(anchor='w', pady=(0, 20))

        # Quick action buttons
        actions_container = tk.Frame(activity_frame, bg=self.colors['white'])
        actions_container.pack(fill='x')

        quick_actions = [
            ("👥 Manage Users", self.user_mgmt.show_user_management, self.colors['secondary']),
            ("📊 View Reports", self.show_reports, self.colors['success']),
            ("💰 Financial Overview", self.financial_mgmt.show_financial_management, self.colors['warning']),
            ("📈 System Analytics", self.show_reports, self.colors['accent'])
        ]

        for i, (text, command, color) in enumerate(quick_actions):
            btn = tk.Button(actions_container,
                           text=text,
                           command=command,
                           font=('Segoe UI', 12, 'bold'),
                           fg=self.colors['white'],
                           bg=color,
                           activeforeground=self.colors['white'],
                           activebackground=self.colors['dark'],
                           relief='flat',
                           borderwidth=0,
                           cursor='hand2',
                           padx=20,
                           pady=15)
            btn.grid(row=i//2, column=i%2, padx=10, pady=10, sticky='ew')

        # Configure grid weights for actions
        actions_container.columnconfigure(0, weight=1)
        actions_container.columnconfigure(1, weight=1)

    def create_stat_card(self, parent, icon, title, value, color, index):
        """Create a modern statistics card"""
        # Card container
        card = tk.Frame(parent, bg=self.colors['white'], relief='solid', borderwidth=1)
        card.grid(row=0, column=index, padx=15, pady=10, sticky='ew', ipadx=20, ipady=20)

        # Configure grid weight
        parent.columnconfigure(index, weight=1)

        # Icon and color bar
        icon_frame = tk.Frame(card, bg=color, height=5)
        icon_frame.pack(fill='x')

        # Card content
        content_frame = tk.Frame(card, bg=self.colors['white'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Icon
        icon_label = tk.Label(content_frame,
                             text=icon,
                             font=('Segoe UI', 24),
                             fg=color,
                             bg=self.colors['white'])
        icon_label.pack()

        # Value
        value_label = tk.Label(content_frame,
                              text=str(value),
                              font=('Segoe UI', 28, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['white'])
        value_label.pack(pady=(10, 5))

        # Title
        title_label = tk.Label(content_frame,
                              text=title,
                              font=('Segoe UI', 12),
                              fg=self.colors['dark'],
                              bg=self.colors['white'])
        title_label.pack()
    
    def show_teacher_dashboard(self, parent):
        """Show modern teacher dashboard"""
        # Welcome message for teachers
        welcome_frame = tk.Frame(parent, bg=self.colors['white'])
        welcome_frame.pack(fill='both', expand=True, pady=50)

        welcome_label = tk.Label(welcome_frame,
                                text="Welcome to Your Teacher Dashboard!",
                                font=('Segoe UI', 20, 'bold'),
                                fg=self.colors['primary'],
                                bg=self.colors['white'])
        welcome_label.pack(pady=(0, 20))

        info_label = tk.Label(welcome_frame,
                             text="Use the navigation menu to manage student results,\nview your classes, and generate reports.",
                             font=('Segoe UI', 14),
                             fg=self.colors['dark'],
                             bg=self.colors['white'],
                             justify='center')
        info_label.pack()

        # Quick action buttons
        actions_container = tk.Frame(welcome_frame, bg=self.colors['white'])
        actions_container.pack(pady=30)

        teacher_actions = [
            ("✏️ Enter Results", self.result_mgmt.show_result_entry, self.colors['secondary']),
            ("👨‍🎓 My Students", self.result_mgmt.show_my_students, self.colors['success'])
        ]

        for i, (text, command, color) in enumerate(teacher_actions):
            btn = tk.Button(actions_container,
                           text=text,
                           command=command,
                           font=('Segoe UI', 12, 'bold'),
                           fg=self.colors['white'],
                           bg=color,
                           activeforeground=self.colors['white'],
                           activebackground=self.colors['dark'],
                           relief='flat',
                           borderwidth=0,
                           cursor='hand2',
                           padx=30,
                           pady=15)
            btn.grid(row=0, column=i, padx=15, pady=10)

    def show_student_dashboard(self, parent):
        """Show modern student dashboard"""
        # Welcome message for students
        welcome_frame = tk.Frame(parent, bg=self.colors['white'])
        welcome_frame.pack(fill='both', expand=True, pady=50)

        welcome_label = tk.Label(welcome_frame,
                                text="Welcome to Your Student Portal!",
                                font=('Segoe UI', 20, 'bold'),
                                fg=self.colors['primary'],
                                bg=self.colors['white'])
        welcome_label.pack(pady=(0, 20))

        info_label = tk.Label(welcome_frame,
                             text="Access your exam results and fee information\nusing the navigation menu.",
                             font=('Segoe UI', 14),
                             fg=self.colors['dark'],
                             bg=self.colors['white'],
                             justify='center')
        info_label.pack()

        # Quick action buttons
        actions_container = tk.Frame(welcome_frame, bg=self.colors['white'])
        actions_container.pack(pady=30)

        student_actions = [
            ("🎯 My Results", self.result_mgmt.show_my_results, self.colors['secondary']),
            ("💳 Fee Status", self.financial_mgmt.show_my_fees, self.colors['success'])
        ]

        for i, (text, command, color) in enumerate(student_actions):
            btn = tk.Button(actions_container,
                           text=text,
                           command=command,
                           font=('Segoe UI', 12, 'bold'),
                           fg=self.colors['white'],
                           bg=color,
                           activeforeground=self.colors['white'],
                           activebackground=self.colors['dark'],
                           relief='flat',
                           borderwidth=0,
                           cursor='hand2',
                           padx=30,
                           pady=15)
            btn.grid(row=0, column=i, padx=15, pady=10)
    
    def show_reports(self):
        """Show comprehensive reports section"""
        self.clear_content()

        title_label = ttk.Label(self.content_frame, text="System Reports", style='Title.TLabel')
        title_label.pack(pady=10)

        # Create notebook for different report categories
        reports_notebook = ttk.Notebook(self.content_frame)
        reports_notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Create report tabs
        self.academic_reports_tab = ttk.Frame(reports_notebook)
        self.financial_reports_tab = ttk.Frame(reports_notebook)
        self.user_reports_tab = ttk.Frame(reports_notebook)
        self.system_reports_tab = ttk.Frame(reports_notebook)

        reports_notebook.add(self.academic_reports_tab, text="Academic Reports")
        reports_notebook.add(self.financial_reports_tab, text="Financial Reports")
        reports_notebook.add(self.user_reports_tab, text="User Reports")
        reports_notebook.add(self.system_reports_tab, text="System Reports")

        # Setup report tabs
        self.setup_academic_reports()
        self.setup_financial_reports()
        self.setup_user_reports()
        self.setup_system_reports()

    def setup_academic_reports(self):
        """Setup academic reports tab"""
        # Report selection frame
        selection_frame = ttk.LabelFrame(self.academic_reports_tab, text="Generate Academic Reports", padding=20)
        selection_frame.pack(fill='x', padx=10, pady=10)

        # Report type selection
        ttk.Label(selection_frame, text="Report Type:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.academic_report_var = tk.StringVar(value="Class Performance")
        academic_combo = ttk.Combobox(selection_frame, textvariable=self.academic_report_var,
                                    values=["Class Performance", "Subject Analysis", "Exam Results Summary",
                                           "Student Progress Report", "Grade Distribution"],
                                    state="readonly", width=25)
        academic_combo.grid(row=0, column=1, padx=5, pady=5)

        # Class filter
        ttk.Label(selection_frame, text="Class:").grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.academic_class_var = tk.StringVar()
        class_combo = ttk.Combobox(selection_frame, textvariable=self.academic_class_var, width=15)
        class_combo.grid(row=0, column=3, padx=5, pady=5)

        # Generate button
        generate_btn = ttk.Button(selection_frame, text="Generate Report",
                                command=self.generate_academic_report)
        generate_btn.grid(row=0, column=4, padx=20, pady=5)

        # Report display area
        report_frame = ttk.Frame(self.academic_reports_tab)
        report_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.academic_report_text = tk.Text(report_frame, wrap=tk.WORD, font=('Courier', 10))
        academic_scrollbar = ttk.Scrollbar(report_frame, orient='vertical',
                                         command=self.academic_report_text.yview)
        self.academic_report_text.configure(yscrollcommand=academic_scrollbar.set)

        self.academic_report_text.pack(side='left', fill='both', expand=True)
        academic_scrollbar.pack(side='right', fill='y')

        # Load class options
        self.load_class_options(class_combo)

    def setup_financial_reports(self):
        """Setup financial reports tab"""
        # Report selection frame
        selection_frame = ttk.LabelFrame(self.financial_reports_tab, text="Generate Financial Reports", padding=20)
        selection_frame.pack(fill='x', padx=10, pady=10)

        # Report type selection
        ttk.Label(selection_frame, text="Report Type:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.financial_report_var = tk.StringVar(value="Monthly Collection")
        financial_combo = ttk.Combobox(selection_frame, textvariable=self.financial_report_var,
                                     values=["Monthly Collection", "Outstanding Fees", "Payment History",
                                            "Class-wise Collection", "Fee Type Analysis"],
                                     state="readonly", width=25)
        financial_combo.grid(row=0, column=1, padx=5, pady=5)

        # Generate button
        generate_btn = ttk.Button(selection_frame, text="Generate Report",
                                command=self.generate_financial_report)
        generate_btn.grid(row=0, column=2, padx=20, pady=5)

        # Report display area
        report_frame = ttk.Frame(self.financial_reports_tab)
        report_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.financial_report_text = tk.Text(report_frame, wrap=tk.WORD, font=('Courier', 10))
        financial_scrollbar = ttk.Scrollbar(report_frame, orient='vertical',
                                          command=self.financial_report_text.yview)
        self.financial_report_text.configure(yscrollcommand=financial_scrollbar.set)

        self.financial_report_text.pack(side='left', fill='both', expand=True)
        financial_scrollbar.pack(side='right', fill='y')

    def setup_user_reports(self):
        """Setup user reports tab"""
        # Report selection frame
        selection_frame = ttk.LabelFrame(self.user_reports_tab, text="Generate User Reports", padding=20)
        selection_frame.pack(fill='x', padx=10, pady=10)

        # Report type selection
        ttk.Label(selection_frame, text="Report Type:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.user_report_var = tk.StringVar(value="User Summary")
        user_combo = ttk.Combobox(selection_frame, textvariable=self.user_report_var,
                                values=["User Summary", "Active Users", "Inactive Users",
                                       "Role Distribution", "Recent Registrations"],
                                state="readonly", width=25)
        user_combo.grid(row=0, column=1, padx=5, pady=5)

        # Generate button
        generate_btn = ttk.Button(selection_frame, text="Generate Report",
                                command=self.generate_user_report)
        generate_btn.grid(row=0, column=2, padx=20, pady=5)

        # Report display area
        report_frame = ttk.Frame(self.user_reports_tab)
        report_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.user_report_text = tk.Text(report_frame, wrap=tk.WORD, font=('Courier', 10))
        user_scrollbar = ttk.Scrollbar(report_frame, orient='vertical',
                                     command=self.user_report_text.yview)
        self.user_report_text.configure(yscrollcommand=user_scrollbar.set)

        self.user_report_text.pack(side='left', fill='both', expand=True)
        user_scrollbar.pack(side='right', fill='y')

    def setup_system_reports(self):
        """Setup system reports tab"""
        # Report selection frame
        selection_frame = ttk.LabelFrame(self.system_reports_tab, text="Generate System Reports", padding=20)
        selection_frame.pack(fill='x', padx=10, pady=10)

        # Report type selection
        ttk.Label(selection_frame, text="Report Type:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.system_report_var = tk.StringVar(value="Database Statistics")
        system_combo = ttk.Combobox(selection_frame, textvariable=self.system_report_var,
                                  values=["Database Statistics", "Data Integrity Check",
                                         "System Usage", "Performance Metrics"],
                                  state="readonly", width=25)
        system_combo.grid(row=0, column=1, padx=5, pady=5)

        # Generate button
        generate_btn = ttk.Button(selection_frame, text="Generate Report",
                                command=self.generate_system_report)
        generate_btn.grid(row=0, column=2, padx=20, pady=5)

        # Report display area
        report_frame = ttk.Frame(self.system_reports_tab)
        report_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.system_report_text = tk.Text(report_frame, wrap=tk.WORD, font=('Courier', 10))
        system_scrollbar = ttk.Scrollbar(report_frame, orient='vertical',
                                       command=self.system_report_text.yview)
        self.system_report_text.configure(yscrollcommand=system_scrollbar.set)

        self.system_report_text.pack(side='left', fill='both', expand=True)
        system_scrollbar.pack(side='right', fill='y')

    def load_class_options(self, combo_widget):
        """Load class options for reports"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT DISTINCT class_name FROM students ORDER BY class_name")
        classes = [row[0] for row in cursor.fetchall()]
        classes.insert(0, "All Classes")  # Add option for all classes

        combo_widget['values'] = classes
        conn.close()

    def generate_academic_report(self):
        """Generate academic reports"""
        report_type = self.academic_report_var.get()
        class_filter = self.academic_class_var.get()

        self.academic_report_text.delete(1.0, tk.END)
        self.academic_report_text.insert(tk.END, f"Generating {report_type} Report...\n\n")

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if report_type == "Class Performance":
                self.generate_class_performance_report(cursor, class_filter)
            elif report_type == "Subject Analysis":
                self.generate_subject_analysis_report(cursor, class_filter)
            elif report_type == "Exam Results Summary":
                self.generate_exam_results_summary(cursor, class_filter)
            elif report_type == "Student Progress Report":
                self.generate_student_progress_report(cursor, class_filter)
            elif report_type == "Grade Distribution":
                self.generate_grade_distribution_report(cursor, class_filter)
        except Exception as e:
            self.academic_report_text.insert(tk.END, f"Error generating report: {str(e)}")
        finally:
            conn.close()

    def generate_class_performance_report(self, cursor, class_filter):
        """Generate class performance report"""
        self.academic_report_text.insert(tk.END, "CLASS PERFORMANCE REPORT\n")
        self.academic_report_text.insert(tk.END, "=" * 60 + "\n\n")

        # Build query based on class filter
        if class_filter and class_filter != "All Classes":
            query = '''
                SELECT s.class_name, COUNT(DISTINCT st.id) as total_students,
                       AVG(r.marks_obtained) as avg_marks,
                       COUNT(r.id) as total_results
                FROM students st
                JOIN users u ON st.user_id = u.id
                LEFT JOIN results r ON st.id = r.student_id
                LEFT JOIN subjects s ON r.subject_id = s.id
                WHERE st.class_name = ? AND u.is_active = 1
                GROUP BY s.class_name
            '''
            cursor.execute(query, (class_filter,))
        else:
            query = '''
                SELECT s.class_name, COUNT(DISTINCT st.id) as total_students,
                       AVG(r.marks_obtained) as avg_marks,
                       COUNT(r.id) as total_results
                FROM students st
                JOIN users u ON st.user_id = u.id
                LEFT JOIN results r ON st.id = r.student_id
                LEFT JOIN subjects s ON r.subject_id = s.id
                WHERE u.is_active = 1
                GROUP BY s.class_name
                ORDER BY s.class_name
            '''
            cursor.execute(query)

        results = cursor.fetchall()

        if results:
            self.academic_report_text.insert(tk.END, f"{'Class':<15} {'Students':<10} {'Avg Marks':<12} {'Total Results':<15}\n")
            self.academic_report_text.insert(tk.END, "-" * 60 + "\n")

            for result in results:
                class_name = result[0] or "N/A"
                students = result[1]
                avg_marks = f"{result[2]:.2f}" if result[2] else "N/A"
                total_results = result[3]

                self.academic_report_text.insert(tk.END,
                    f"{class_name:<15} {students:<10} {avg_marks:<12} {total_results:<15}\n")
        else:
            self.academic_report_text.insert(tk.END, "No data available for the selected criteria.\n")

    def generate_financial_report(self):
        """Generate financial reports"""
        report_type = self.financial_report_var.get()

        self.financial_report_text.delete(1.0, tk.END)
        self.financial_report_text.insert(tk.END, f"Generating {report_type} Report...\n\n")

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if report_type == "Monthly Collection":
                self.generate_monthly_collection_report(cursor)
            elif report_type == "Outstanding Fees":
                self.generate_outstanding_fees_report(cursor)
            elif report_type == "Payment History":
                self.generate_payment_history_report(cursor)
            elif report_type == "Class-wise Collection":
                self.generate_classwise_collection_report(cursor)
            elif report_type == "Fee Type Analysis":
                self.generate_fee_type_analysis_report(cursor)
        except Exception as e:
            self.financial_report_text.insert(tk.END, f"Error generating report: {str(e)}")
        finally:
            conn.close()

    def generate_monthly_collection_report(self, cursor):
        """Generate monthly collection report"""
        self.financial_report_text.insert(tk.END, "MONTHLY COLLECTION REPORT\n")
        self.financial_report_text.insert(tk.END, "=" * 60 + "\n\n")

        cursor.execute('''
            SELECT strftime('%Y-%m', payment_date) as month,
                   SUM(amount_paid) as total_collection,
                   COUNT(*) as total_payments,
                   AVG(amount_paid) as avg_payment
            FROM fee_payments
            GROUP BY strftime('%Y-%m', payment_date)
            ORDER BY month DESC
            LIMIT 12
        ''')

        results = cursor.fetchall()

        if results:
            self.financial_report_text.insert(tk.END,
                f"{'Month':<12} {'Collection':<15} {'Payments':<10} {'Avg Payment':<15}\n")
            self.financial_report_text.insert(tk.END, "-" * 60 + "\n")

            total_collection = 0
            for result in results:
                month = result[0]
                collection = result[1]
                payments = result[2]
                avg_payment = result[3]
                total_collection += collection

                self.financial_report_text.insert(tk.END,
                    f"{month:<12} ${collection:<14.2f} {payments:<10} ${avg_payment:<14.2f}\n")

            self.financial_report_text.insert(tk.END, "-" * 60 + "\n")
            self.financial_report_text.insert(tk.END, f"TOTAL COLLECTION: ${total_collection:.2f}\n")
        else:
            self.financial_report_text.insert(tk.END, "No payment data available.\n")

    def generate_user_report(self):
        """Generate user reports"""
        report_type = self.user_report_var.get()

        self.user_report_text.delete(1.0, tk.END)
        self.user_report_text.insert(tk.END, f"Generating {report_type} Report...\n\n")

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if report_type == "User Summary":
                self.generate_user_summary_report(cursor)
            elif report_type == "Active Users":
                self.generate_active_users_report(cursor)
            elif report_type == "Role Distribution":
                self.generate_role_distribution_report(cursor)
        except Exception as e:
            self.user_report_text.insert(tk.END, f"Error generating report: {str(e)}")
        finally:
            conn.close()

    def generate_user_summary_report(self, cursor):
        """Generate user summary report"""
        self.user_report_text.insert(tk.END, "USER SUMMARY REPORT\n")
        self.user_report_text.insert(tk.END, "=" * 50 + "\n\n")

        # Get user counts by role
        cursor.execute('''
            SELECT role,
                   COUNT(*) as total,
                   SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                   SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive
            FROM users
            GROUP BY role
            ORDER BY role
        ''')

        results = cursor.fetchall()

        if results:
            self.user_report_text.insert(tk.END,
                f"{'Role':<12} {'Total':<8} {'Active':<8} {'Inactive':<10}\n")
            self.user_report_text.insert(tk.END, "-" * 40 + "\n")

            total_users = 0
            total_active = 0

            for result in results:
                role = result[0].title()
                total = result[1]
                active = result[2]
                inactive = result[3]

                total_users += total
                total_active += active

                self.user_report_text.insert(tk.END,
                    f"{role:<12} {total:<8} {active:<8} {inactive:<10}\n")

            self.user_report_text.insert(tk.END, "-" * 40 + "\n")
            self.user_report_text.insert(tk.END,
                f"{'TOTAL':<12} {total_users:<8} {total_active:<8} {total_users-total_active:<10}\n")
        else:
            self.user_report_text.insert(tk.END, "No user data available.\n")

    def generate_system_report(self):
        """Generate system reports"""
        report_type = self.system_report_var.get()

        self.system_report_text.delete(1.0, tk.END)
        self.system_report_text.insert(tk.END, f"Generating {report_type} Report...\n\n")

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if report_type == "Database Statistics":
                self.generate_database_statistics_report(cursor)
            elif report_type == "Data Integrity Check":
                self.generate_data_integrity_report(cursor)
        except Exception as e:
            self.system_report_text.insert(tk.END, f"Error generating report: {str(e)}")
        finally:
            conn.close()

    def generate_database_statistics_report(self, cursor):
        """Generate database statistics report"""
        self.system_report_text.insert(tk.END, "DATABASE STATISTICS REPORT\n")
        self.system_report_text.insert(tk.END, "=" * 50 + "\n\n")

        # Get table statistics
        tables = ['users', 'students', 'teachers', 'subjects', 'exams', 'results',
                 'fee_structure', 'fee_payments']

        self.system_report_text.insert(tk.END, f"{'Table':<15} {'Records':<10}\n")
        self.system_report_text.insert(tk.END, "-" * 30 + "\n")

        total_records = 0
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            total_records += count

            self.system_report_text.insert(tk.END, f"{table:<15} {count:<10}\n")

        self.system_report_text.insert(tk.END, "-" * 30 + "\n")
        self.system_report_text.insert(tk.END, f"{'TOTAL':<15} {total_records:<10}\n")

        # Database file size
        import os
        if os.path.exists("student_management.db"):
            size = os.path.getsize("student_management.db")
            size_mb = size / (1024 * 1024)
            self.system_report_text.insert(tk.END, f"\nDatabase Size: {size_mb:.2f} MB\n")

    def show_teacher_reports(self):
        """Show teacher-specific reports"""
        self.clear_content()

        title_label = ttk.Label(self.content_frame, text="My Reports", style='Title.TLabel')
        title_label.pack(pady=10)

        # Get current teacher data
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT t.id, t.teacher_id, u.full_name
            FROM teachers t
            JOIN users u ON t.user_id = u.id
            WHERE u.id = ?
        ''', (self.current_user['id'],))

        teacher_data = cursor.fetchone()

        if not teacher_data:
            error_label = ttk.Label(self.content_frame,
                                  text="Teacher profile not found. Please contact administrator.")
            error_label.pack(pady=20)
            conn.close()
            return

        # Create notebook for different report types
        reports_notebook = ttk.Notebook(self.content_frame)
        reports_notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Create report tabs
        self.my_subjects_report_tab = ttk.Frame(reports_notebook)
        self.student_performance_tab = ttk.Frame(reports_notebook)
        self.exam_analysis_tab = ttk.Frame(reports_notebook)

        reports_notebook.add(self.my_subjects_report_tab, text="My Subjects Report")
        reports_notebook.add(self.student_performance_tab, text="Student Performance")
        reports_notebook.add(self.exam_analysis_tab, text="Exam Analysis")

        # Setup report tabs
        self.setup_teacher_subjects_report(teacher_data[0], cursor)
        self.setup_teacher_performance_report(teacher_data[0], cursor)
        self.setup_teacher_exam_analysis(teacher_data[0], cursor)

    def setup_teacher_performance_report(self, teacher_id, cursor):
        """Setup teacher performance report tab"""
        # Simple performance overview
        overview_frame = ttk.LabelFrame(self.student_performance_tab, text="Performance Overview", padding=10)
        overview_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Get overall performance statistics with better handling
        cursor.execute('''
            SELECT sub.subject_name, sub.class_name, sub.pass_marks,
                   COUNT(r.id) as total_results,
                   COALESCE(AVG(r.marks_obtained), 0) as avg_marks,
                   COUNT(CASE WHEN r.marks_obtained >= sub.pass_marks THEN 1 END) as passed_count
            FROM subjects sub
            LEFT JOIN results r ON sub.id = r.subject_id
            WHERE sub.teacher_id = ?
            GROUP BY sub.id, sub.subject_name, sub.class_name, sub.pass_marks
            ORDER BY sub.class_name, sub.subject_name
        ''', (teacher_id,))

        performance_data = cursor.fetchall()

        # Performance display
        perf_text = tk.Text(overview_frame, wrap=tk.WORD, font=('Courier', 10), height=15)
        perf_scrollbar = ttk.Scrollbar(overview_frame, orient='vertical', command=perf_text.yview)
        perf_text.configure(yscrollcommand=perf_scrollbar.set)

        perf_text.pack(side='left', fill='both', expand=True)
        perf_scrollbar.pack(side='right', fill='y')

        # Populate performance data
        perf_text.insert(tk.END, "OVERALL PERFORMANCE SUMMARY\n")
        perf_text.insert(tk.END, "=" * 70 + "\n\n")

        if performance_data:
            perf_text.insert(tk.END, f"{'Subject':<20} {'Class':<12} {'Results':<8} {'Avg':<8} {'Pass%':<10}\n")
            perf_text.insert(tk.END, "-" * 70 + "\n")

            total_subjects = 0
            subjects_with_results = 0

            for data in performance_data:
                subject = data[0]
                class_name = data[1]
                pass_marks = data[2]
                total_results = data[3]
                avg_marks = data[4]
                passed_count = data[5]

                total_subjects += 1

                if total_results > 0:
                    subjects_with_results += 1
                    avg_display = f"{avg_marks:.1f}"
                    pass_rate = f"{(passed_count/total_results)*100:.1f}%"
                else:
                    avg_display = "No Data"
                    pass_rate = "No Data"

                perf_text.insert(tk.END, f"{subject:<20} {class_name:<12} {total_results:<8} {avg_display:<8} {pass_rate:<10}\n")

            # Add summary
            perf_text.insert(tk.END, "-" * 70 + "\n")
            perf_text.insert(tk.END, f"Summary: {total_subjects} subjects assigned, {subjects_with_results} have results\n")
        else:
            perf_text.insert(tk.END, "No subjects assigned to you yet.\n")

        perf_text.config(state='disabled')

    def setup_teacher_exam_analysis(self, teacher_id, cursor):
        """Setup teacher exam analysis tab"""
        # Exam analysis overview
        analysis_frame = ttk.LabelFrame(self.exam_analysis_tab, text="Exam Analysis", padding=10)
        analysis_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Get exam-wise analysis
        cursor.execute('''
            SELECT e.exam_name, e.exam_type, sub.subject_name,
                   COUNT(r.id) as total_results,
                   AVG(r.marks_obtained) as avg_marks,
                   MIN(r.marks_obtained) as min_marks,
                   MAX(r.marks_obtained) as max_marks
            FROM exams e
            JOIN results r ON e.id = r.exam_id
            JOIN subjects sub ON r.subject_id = sub.id
            WHERE sub.teacher_id = ?
            GROUP BY e.id, sub.id
            ORDER BY e.exam_date DESC, sub.subject_name
        ''', (teacher_id,))

        exam_data = cursor.fetchall()

        # Analysis display
        analysis_text = tk.Text(analysis_frame, wrap=tk.WORD, font=('Courier', 10), height=15)
        analysis_scrollbar = ttk.Scrollbar(analysis_frame, orient='vertical', command=analysis_text.yview)
        analysis_text.configure(yscrollcommand=analysis_scrollbar.set)

        analysis_text.pack(side='left', fill='both', expand=True)
        analysis_scrollbar.pack(side='right', fill='y')

        # Populate exam analysis
        analysis_text.insert(tk.END, "EXAM-WISE ANALYSIS\n")
        analysis_text.insert(tk.END, "=" * 80 + "\n\n")

        if exam_data:
            current_exam = None
            for data in exam_data:
                exam_name = data[0]
                exam_type = data[1]
                subject = data[2]
                total_results = data[3]
                avg_marks = data[4]
                min_marks = data[5]
                max_marks = data[6]

                if exam_name != current_exam:
                    if current_exam:
                        analysis_text.insert(tk.END, "\n")
                    analysis_text.insert(tk.END, f"EXAM: {exam_name} ({exam_type})\n")
                    analysis_text.insert(tk.END, "-" * 50 + "\n")
                    current_exam = exam_name

                analysis_text.insert(tk.END,
                    f"  {subject}: {total_results} results, Avg: {avg_marks:.1f}, Range: {min_marks}-{max_marks}\n")
        else:
            analysis_text.insert(tk.END, "No exam data available yet.\n")

        analysis_text.config(state='disabled')

    def setup_teacher_subjects_report(self, teacher_id, cursor):
        """Setup teacher subjects report"""
        # Report controls
        controls_frame = ttk.LabelFrame(self.my_subjects_report_tab, text="Generate Subject Report", padding=20)
        controls_frame.pack(fill='x', padx=10, pady=10)

        # Subject selection
        ttk.Label(controls_frame, text="Subject:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.teacher_subject_var = tk.StringVar()
        subject_combo = ttk.Combobox(controls_frame, textvariable=self.teacher_subject_var, state="readonly", width=25)
        subject_combo.grid(row=0, column=1, padx=5, pady=5)

        # Load teacher's subjects
        cursor.execute('''
            SELECT id, subject_name, class_name
            FROM subjects
            WHERE teacher_id = ?
            ORDER BY class_name, subject_name
        ''', (teacher_id,))

        subjects = cursor.fetchall()
        subject_list = [f"{subject[1]} ({subject[2]})" for subject in subjects]
        subject_combo['values'] = subject_list
        self.teacher_subject_data = {f"{subject[1]} ({subject[2]})": subject for subject in subjects}

        # Generate button
        generate_btn = ttk.Button(controls_frame, text="Generate Report",
                                command=lambda: self.generate_teacher_subject_report(teacher_id))
        generate_btn.grid(row=0, column=2, padx=20, pady=5)

        # Report display area
        report_frame = ttk.Frame(self.my_subjects_report_tab)
        report_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.teacher_subject_report_text = tk.Text(report_frame, wrap=tk.WORD, font=('Courier', 10))
        scrollbar = ttk.Scrollbar(report_frame, orient='vertical', command=self.teacher_subject_report_text.yview)
        self.teacher_subject_report_text.configure(yscrollcommand=scrollbar.set)

        self.teacher_subject_report_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

    def generate_teacher_subject_report(self, teacher_id):
        """Generate teacher subject report"""
        subject_selection = self.teacher_subject_var.get()
        if not subject_selection or subject_selection not in self.teacher_subject_data:
            messagebox.showwarning("Warning", "Please select a subject")
            return

        subject = self.teacher_subject_data[subject_selection]
        subject_id = subject[0]

        self.teacher_subject_report_text.delete(1.0, tk.END)
        self.teacher_subject_report_text.insert(tk.END, f"SUBJECT PERFORMANCE REPORT\n")
        self.teacher_subject_report_text.insert(tk.END, f"Subject: {subject[1]} ({subject[2]})\n")
        self.teacher_subject_report_text.insert(tk.END, "=" * 60 + "\n\n")

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        try:
            # Get detailed results for this subject
            cursor.execute('''
                SELECT u.full_name, s.student_id, e.exam_name, r.marks_obtained,
                       sub.max_marks, r.grade, r.entered_at
                FROM results r
                JOIN students s ON r.student_id = s.id
                JOIN users u ON s.user_id = u.id
                JOIN exams e ON r.exam_id = e.id
                JOIN subjects sub ON r.subject_id = sub.id
                WHERE r.subject_id = ?
                ORDER BY e.exam_name, u.full_name
            ''', (subject_id,))

            results = cursor.fetchall()

            if results:
                # Group by exam
                current_exam = None
                exam_totals = {}

                for result in results:
                    exam_name = result[2]

                    if exam_name != current_exam:
                        if current_exam:
                            self.teacher_subject_report_text.insert(tk.END, "\n")

                        self.teacher_subject_report_text.insert(tk.END, f"EXAM: {exam_name}\n")
                        self.teacher_subject_report_text.insert(tk.END, "-" * 40 + "\n")
                        self.teacher_subject_report_text.insert(tk.END,
                            f"{'Student':<20} {'ID':<10} {'Marks':<10} {'Grade':<8}\n")
                        self.teacher_subject_report_text.insert(tk.END, "-" * 50 + "\n")
                        current_exam = exam_name
                        exam_totals[exam_name] = {'total': 0, 'count': 0, 'passed': 0}

                    # Add student result
                    self.teacher_subject_report_text.insert(tk.END,
                        f"{result[0]:<20} {result[1]:<10} {result[3]:<10} {result[5]:<8}\n")

                    # Update totals
                    exam_totals[exam_name]['total'] += result[3]
                    exam_totals[exam_name]['count'] += 1
                    if result[3] >= 40:  # Assuming 40 is pass marks
                        exam_totals[exam_name]['passed'] += 1

                # Summary
                self.teacher_subject_report_text.insert(tk.END, "\n" + "=" * 60 + "\n")
                self.teacher_subject_report_text.insert(tk.END, "SUMMARY\n")
                self.teacher_subject_report_text.insert(tk.END, "=" * 60 + "\n")

                for exam_name, totals in exam_totals.items():
                    avg = totals['total'] / totals['count'] if totals['count'] > 0 else 0
                    pass_rate = (totals['passed'] / totals['count']) * 100 if totals['count'] > 0 else 0

                    self.teacher_subject_report_text.insert(tk.END,
                        f"{exam_name}: Avg: {avg:.2f}, Pass Rate: {pass_rate:.1f}%\n")
            else:
                self.teacher_subject_report_text.insert(tk.END, "No results found for this subject.\n")

        except Exception as e:
            self.teacher_subject_report_text.insert(tk.END, f"Error generating report: {str(e)}")
        finally:
            conn.close()

    def show_class_analysis(self):
        """Show class analysis for teacher"""
        self.clear_content()

        title_label = ttk.Label(self.content_frame, text="Class Analysis", style='Title.TLabel')
        title_label.pack(pady=10)

        # Get current teacher data
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT t.id, t.teacher_id, u.full_name
            FROM teachers t
            JOIN users u ON t.user_id = u.id
            WHERE u.id = ?
        ''', (self.current_user['id'],))

        teacher_data = cursor.fetchone()

        if not teacher_data:
            error_label = ttk.Label(self.content_frame,
                                  text="Teacher profile not found. Please contact administrator.")
            error_label.pack(pady=20)
            conn.close()
            return

        # Class selection frame
        selection_frame = ttk.LabelFrame(self.content_frame, text="Select Class for Analysis", padding=20)
        selection_frame.pack(fill='x', padx=10, pady=10)

        # Get classes where teacher teaches
        cursor.execute('''
            SELECT DISTINCT class_name
            FROM subjects
            WHERE teacher_id = ?
            ORDER BY class_name
        ''', (teacher_data[0],))

        classes = cursor.fetchall()

        if not classes:
            no_classes_label = ttk.Label(selection_frame, text="No classes assigned to you.")
            no_classes_label.pack(pady=10)
            conn.close()
            return

        ttk.Label(selection_frame, text="Class:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.analysis_class_var = tk.StringVar()
        class_combo = ttk.Combobox(selection_frame, textvariable=self.analysis_class_var,
                                  values=[cls[0] for cls in classes], state="readonly", width=20)
        class_combo.grid(row=0, column=1, padx=5, pady=5)

        generate_btn = ttk.Button(selection_frame, text="Generate Analysis",
                                command=lambda: self.generate_class_analysis(teacher_data[0]))
        generate_btn.grid(row=0, column=2, padx=20, pady=5)

        # Analysis display area
        analysis_frame = ttk.Frame(self.content_frame)
        analysis_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.class_analysis_text = tk.Text(analysis_frame, wrap=tk.WORD, font=('Courier', 10))
        analysis_scrollbar = ttk.Scrollbar(analysis_frame, orient='vertical', command=self.class_analysis_text.yview)
        self.class_analysis_text.configure(yscrollcommand=analysis_scrollbar.set)

        self.class_analysis_text.pack(side='left', fill='both', expand=True)
        analysis_scrollbar.pack(side='right', fill='y')

        conn.close()

    def generate_class_analysis(self, teacher_id):
        """Generate class analysis report"""
        class_name = self.analysis_class_var.get()
        if not class_name:
            messagebox.showwarning("Warning", "Please select a class")
            return

        self.class_analysis_text.delete(1.0, tk.END)
        self.class_analysis_text.insert(tk.END, f"CLASS ANALYSIS REPORT\n")
        self.class_analysis_text.insert(tk.END, f"Class: {class_name}\n")
        self.class_analysis_text.insert(tk.END, "=" * 60 + "\n\n")

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        try:
            # Get class statistics
            cursor.execute('''
                SELECT COUNT(DISTINCT s.id) as total_students
                FROM students s
                JOIN users u ON s.user_id = u.id
                WHERE s.class_name = ? AND u.is_active = 1
            ''', (class_name,))

            total_students = cursor.fetchone()[0]

            # Get subject-wise performance for this teacher's subjects
            cursor.execute('''
                SELECT sub.subject_name, sub.pass_marks,
                       COUNT(r.id) as total_results,
                       COALESCE(AVG(r.marks_obtained), 0) as avg_marks,
                       MIN(r.marks_obtained) as min_marks,
                       MAX(r.marks_obtained) as max_marks,
                       COUNT(CASE WHEN r.marks_obtained >= sub.pass_marks THEN 1 END) as passed
                FROM subjects sub
                LEFT JOIN results r ON sub.id = r.subject_id
                LEFT JOIN students s ON r.student_id = s.id
                WHERE sub.teacher_id = ? AND sub.class_name = ?
                GROUP BY sub.id, sub.subject_name, sub.pass_marks
                ORDER BY sub.subject_name
            ''', (teacher_id, class_name))

            subject_performance = cursor.fetchall()

            self.class_analysis_text.insert(tk.END, f"Total Students in Class: {total_students}\n\n")

            if subject_performance:
                self.class_analysis_text.insert(tk.END, "SUBJECT-WISE PERFORMANCE\n")
                self.class_analysis_text.insert(tk.END, "-" * 70 + "\n")
                self.class_analysis_text.insert(tk.END,
                    f"{'Subject':<15} {'Results':<8} {'Avg':<8} {'Min':<6} {'Max':<6} {'Pass%':<10}\n")
                self.class_analysis_text.insert(tk.END, "-" * 70 + "\n")

                for perf in subject_performance:
                    subject_name = perf[0]
                    pass_marks = perf[1]
                    total_results = perf[2]
                    avg_marks = perf[3]
                    min_marks = perf[4]
                    max_marks = perf[5]
                    passed = perf[6]

                    if total_results > 0:
                        avg_display = f"{avg_marks:.1f}"
                        min_display = str(int(min_marks)) if min_marks is not None else "N/A"
                        max_display = str(int(max_marks)) if max_marks is not None else "N/A"
                        pass_rate = f"{(passed/total_results)*100:.1f}%"
                    else:
                        avg_display = "No Data"
                        min_display = "No Data"
                        max_display = "No Data"
                        pass_rate = "No Data"

                    self.class_analysis_text.insert(tk.END,
                        f"{subject_name:<15} {total_results:<8} {avg_display:<8} {min_display:<6} {max_display:<6} {pass_rate:<10}\n")

                # Add summary
                subjects_with_results = sum(1 for perf in subject_performance if perf[2] > 0)
                self.class_analysis_text.insert(tk.END, "-" * 70 + "\n")
                self.class_analysis_text.insert(tk.END, f"Summary: {len(subject_performance)} subjects, {subjects_with_results} have results\n")
            else:
                self.class_analysis_text.insert(tk.END, "No subjects assigned to you for this class.\n")

        except Exception as e:
            self.class_analysis_text.insert(tk.END, f"Error generating analysis: {str(e)}")
        finally:
            conn.close()
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = StudentManagementSystem()
    app.run()
