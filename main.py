import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Import modules
from database import db_manager
from user_management import UserManagement
from result_management import ResultManagement
from financial_management import FinancialManagement

class StudentManagementSystem:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Student Result Management System")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # Center the window
        self.center_window()
        
        # Current user
        self.current_user = None
        
        # Style configuration
        self.setup_styles()
        
        # Initialize modules
        self.user_mgmt = UserManagement(self)
        self.result_mgmt = ResultManagement(self)
        self.financial_mgmt = FinancialManagement(self)
        
        # Show login screen
        self.show_login()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_styles(self):
        """Setup custom styles for the application"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#f0f0f0')
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'), background='#f0f0f0')
        style.configure('Custom.TButton', font=('Arial', 10))
        style.configure('Login.TFrame', background='#ffffff', relief='raised', borderwidth=2)
        
    def clear_window(self):
        """Clear all widgets from the window"""
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def show_login(self):
        """Show login screen"""
        self.clear_window()
        
        # Main frame
        main_frame = ttk.Frame(self.root, style='Login.TFrame')
        main_frame.place(relx=0.5, rely=0.5, anchor='center')
        
        # Title
        title_label = ttk.Label(main_frame, text="Student Result Management System", 
                               style='Title.TLabel')
        title_label.pack(pady=20)
        
        # Login form
        login_frame = ttk.Frame(main_frame)
        login_frame.pack(padx=40, pady=20)
        
        # Username
        ttk.Label(login_frame, text="Username:", font=('Arial', 10)).grid(row=0, column=0, sticky='e', padx=5, pady=10)
        self.username_entry = ttk.Entry(login_frame, font=('Arial', 10), width=20)
        self.username_entry.grid(row=0, column=1, padx=5, pady=10)
        
        # Password
        ttk.Label(login_frame, text="Password:", font=('Arial', 10)).grid(row=1, column=0, sticky='e', padx=5, pady=10)
        self.password_entry = ttk.Entry(login_frame, font=('Arial', 10), width=20, show='*')
        self.password_entry.grid(row=1, column=1, padx=5, pady=10)
        
        # Login button
        login_btn = ttk.Button(login_frame, text="Login", command=self.login, style='Custom.TButton')
        login_btn.grid(row=2, column=0, columnspan=2, pady=20)
        
        # Bind Enter key to login
        self.root.bind('<Return>', lambda event: self.login())
        
        # Default credentials info
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(pady=10)
        
        ttk.Label(info_frame, text="Default Login:", font=('Arial', 9, 'bold')).pack()
        ttk.Label(info_frame, text="Username: admin", font=('Arial', 8)).pack()
        ttk.Label(info_frame, text="Password: admin123", font=('Arial', 8)).pack()
        
        # Focus on username entry
        self.username_entry.focus()
    
    def login(self):
        """Handle user login"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("Error", "Please enter both username and password")
            return
        
        user = db_manager.authenticate_user(username, password)
        if user:
            self.current_user = user
            self.show_dashboard()
        else:
            messagebox.showerror("Error", "Invalid username or password")
            self.password_entry.delete(0, tk.END)
    
    def logout(self):
        """Handle user logout"""
        self.current_user = None
        self.show_login()
    
    def show_dashboard(self):
        """Show main dashboard based on user role"""
        self.clear_window()
        
        # Main container
        main_container = ttk.Frame(self.root)
        main_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Header
        header_frame = ttk.Frame(main_container)
        header_frame.pack(fill='x', pady=(0, 10))
        
        # Welcome message
        welcome_label = ttk.Label(header_frame, 
                                 text=f"Welcome, {self.current_user['full_name']} ({self.current_user['role'].title()})",
                                 style='Title.TLabel')
        welcome_label.pack(side='left')
        
        # Logout button
        logout_btn = ttk.Button(header_frame, text="Logout", command=self.logout)
        logout_btn.pack(side='right')
        
        # Navigation frame
        nav_frame = ttk.Frame(main_container)
        nav_frame.pack(fill='x', pady=(0, 10))
        
        # Content frame
        self.content_frame = ttk.Frame(main_container)
        self.content_frame.pack(fill='both', expand=True)
        
        # Create navigation buttons based on role
        self.create_navigation(nav_frame)
        
        # Show default content
        self.show_home()
    
    def create_navigation(self, parent):
        """Create navigation buttons based on user role"""
        buttons = []
        
        # Common buttons
        buttons.append(("Home", self.show_home))
        
        if self.current_user['role'] == 'admin':
            buttons.extend([
                ("User Management", self.user_mgmt.show_user_management),
                ("Result Management", self.result_mgmt.show_result_management),
                ("Financial Management", self.financial_mgmt.show_financial_management),
                ("Reports", self.show_reports)
            ])
        elif self.current_user['role'] == 'teacher':
            buttons.extend([
                ("Result Entry", self.result_mgmt.show_result_entry),
                ("My Students", self.result_mgmt.show_my_students),
                ("Reports", self.show_reports)
            ])
        elif self.current_user['role'] == 'student':
            buttons.extend([
                ("My Results", self.result_mgmt.show_my_results),
                ("Fee Status", self.financial_mgmt.show_my_fees)
            ])
        
        # Create buttons
        for text, command in buttons:
            btn = ttk.Button(parent, text=text, command=command, style='Custom.TButton')
            btn.pack(side='left', padx=5)
    
    def clear_content(self):
        """Clear content frame"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_home(self):
        """Show home/dashboard content"""
        self.clear_content()
        
        # Dashboard title
        title_label = ttk.Label(self.content_frame, text="Dashboard", style='Title.TLabel')
        title_label.pack(pady=20)
        
        # Dashboard content based on role
        if self.current_user['role'] == 'admin':
            self.show_admin_dashboard()
        elif self.current_user['role'] == 'teacher':
            self.show_teacher_dashboard()
        elif self.current_user['role'] == 'student':
            self.show_student_dashboard()
    
    def show_admin_dashboard(self):
        """Show admin dashboard with statistics"""
        stats_frame = ttk.Frame(self.content_frame)
        stats_frame.pack(fill='x', padx=20, pady=20)
        
        # Get statistics
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # Count statistics
        cursor.execute("SELECT COUNT(*) FROM students")
        total_students = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM teachers")
        total_teachers = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM subjects")
        total_subjects = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM results")
        total_results = cursor.fetchone()[0]
        
        conn.close()
        
        # Display statistics
        stats = [
            ("Total Students", total_students),
            ("Total Teachers", total_teachers),
            ("Total Subjects", total_subjects),
            ("Total Results", total_results)
        ]
        
        for i, (label, value) in enumerate(stats):
            stat_frame = ttk.LabelFrame(stats_frame, text=label, padding=10)
            stat_frame.grid(row=0, column=i, padx=10, pady=10, sticky='ew')
            
            value_label = ttk.Label(stat_frame, text=str(value), font=('Arial', 20, 'bold'))
            value_label.pack()
        
        # Configure grid weights
        for i in range(len(stats)):
            stats_frame.columnconfigure(i, weight=1)
    
    def show_teacher_dashboard(self):
        """Show teacher dashboard"""
        info_label = ttk.Label(self.content_frame, 
                              text="Welcome to the Teacher Dashboard\nUse the navigation buttons to manage results and view reports.",
                              font=('Arial', 12), justify='center')
        info_label.pack(pady=50)
    
    def show_student_dashboard(self):
        """Show student dashboard"""
        info_label = ttk.Label(self.content_frame, 
                              text="Welcome to the Student Dashboard\nView your results and fee status using the navigation buttons.",
                              font=('Arial', 12), justify='center')
        info_label.pack(pady=50)
    
    def show_reports(self):
        """Show reports section"""
        self.clear_content()
        
        title_label = ttk.Label(self.content_frame, text="Reports", style='Title.TLabel')
        title_label.pack(pady=20)
        
        info_label = ttk.Label(self.content_frame, text="Reports functionality will be implemented here.")
        info_label.pack(pady=20)
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = StudentManagementSystem()
    app.run()
