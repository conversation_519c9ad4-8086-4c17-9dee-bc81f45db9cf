import sqlite3
import hashlib
from datetime import datetime, date
import os

class DatabaseManager:
    def __init__(self, db_name="student_management.db"):
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        return sqlite3.connect(self.db_name)
    
    def init_database(self):
        """Initialize database with all required tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Users table (for authentication)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'teacher', 'student')),
                email VARCHAR(100) UNIQUE,
                full_name VARCHAR(100) NOT NULL,
                phone VARCHAR(15),
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Students table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER UNIQUE,
                student_id VARCHAR(20) UNIQUE NOT NULL,
                class_name VARCHAR(50) NOT NULL,
                section VARCHAR(10),
                roll_number INTEGER,
                date_of_birth DATE,
                guardian_name VARCHAR(100),
                guardian_phone VARCHAR(15),
                admission_date DATE DEFAULT CURRENT_DATE,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        ''')
        
        # Teachers table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS teachers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER UNIQUE,
                teacher_id VARCHAR(20) UNIQUE NOT NULL,
                department VARCHAR(50),
                qualification VARCHAR(100),
                experience_years INTEGER,
                salary DECIMAL(10,2),
                joining_date DATE DEFAULT CURRENT_DATE,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        ''')
        
        # Subjects table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS subjects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                subject_code VARCHAR(20) UNIQUE NOT NULL,
                subject_name VARCHAR(100) NOT NULL,
                class_name VARCHAR(50) NOT NULL,
                max_marks INTEGER DEFAULT 100,
                pass_marks INTEGER DEFAULT 40,
                teacher_id INTEGER,
                FOREIGN KEY (teacher_id) REFERENCES teachers (id)
            )
        ''')
        
        # Exams table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS exams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exam_name VARCHAR(100) NOT NULL,
                exam_type VARCHAR(50) NOT NULL,
                class_name VARCHAR(50) NOT NULL,
                exam_date DATE,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # Results table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                subject_id INTEGER NOT NULL,
                exam_id INTEGER NOT NULL,
                marks_obtained DECIMAL(5,2) NOT NULL,
                grade VARCHAR(5),
                remarks TEXT,
                entered_by INTEGER,
                entered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students (id),
                FOREIGN KEY (subject_id) REFERENCES subjects (id),
                FOREIGN KEY (exam_id) REFERENCES exams (id),
                FOREIGN KEY (entered_by) REFERENCES users (id),
                UNIQUE(student_id, subject_id, exam_id)
            )
        ''')
        
        # Fee Structure table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fee_structure (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                class_name VARCHAR(50) NOT NULL,
                fee_type VARCHAR(50) NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                due_date_day INTEGER DEFAULT 10,
                academic_year VARCHAR(10) NOT NULL,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Fee Payments table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fee_payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                fee_structure_id INTEGER NOT NULL,
                amount_paid DECIMAL(10,2) NOT NULL,
                payment_date DATE DEFAULT CURRENT_DATE,
                payment_method VARCHAR(20) DEFAULT 'cash',
                receipt_number VARCHAR(50),
                remarks TEXT,
                recorded_by INTEGER,
                FOREIGN KEY (student_id) REFERENCES students (id),
                FOREIGN KEY (fee_structure_id) REFERENCES fee_structure (id),
                FOREIGN KEY (recorded_by) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Create default admin user if not exists
        self.create_default_admin()
    
    def create_default_admin(self):
        """Create default admin user"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if admin exists
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if cursor.fetchone() is None:
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name, email)
                VALUES (?, ?, ?, ?, ?)
            ''', ("admin", password_hash, "admin", "System Administrator", "<EMAIL>"))
            conn.commit()
        
        conn.close()
    
    def hash_password(self, password):
        """Hash password using SHA256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password, password_hash):
        """Verify password against hash"""
        return self.hash_password(password) == password_hash
    
    def authenticate_user(self, username, password):
        """Authenticate user and return user data"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, password_hash, role, full_name, email, is_active
            FROM users WHERE username = ? AND is_active = 1
        ''', (username,))
        
        user = cursor.fetchone()
        conn.close()
        
        if user and self.verify_password(password, user[2]):
            return {
                'id': user[0],
                'username': user[1],
                'role': user[3],
                'full_name': user[4],
                'email': user[5]
            }
        return None
    
    def calculate_grade(self, marks, max_marks):
        """Calculate grade based on percentage"""
        percentage = (marks / max_marks) * 100
        if percentage >= 90:
            return 'A+'
        elif percentage >= 80:
            return 'A'
        elif percentage >= 70:
            return 'B+'
        elif percentage >= 60:
            return 'B'
        elif percentage >= 50:
            return 'C+'
        elif percentage >= 40:
            return 'C'
        else:
            return 'F'

# Initialize database
db_manager = DatabaseManager()
