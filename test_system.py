#!/usr/bin/env python3
"""
Test script for Student Result Management System
This script tests all major functionality to ensure the system works correctly.
"""

import sqlite3
import os
import sys
from database import db_manager
from datetime import datetime, date

def test_database_creation():
    """Test database creation and initialization"""
    print("Testing database creation...")
    
    # Check if database file exists
    if os.path.exists("student_management.db"):
        print("✓ Database file created successfully")
    else:
        print("✗ Database file not found")
        return False
    
    # Check if tables exist
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    tables = ['users', 'students', 'teachers', 'subjects', 'exams', 'results', 
              'fee_structure', 'fee_payments']
    
    for table in tables:
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
        if cursor.fetchone():
            print(f"✓ Table '{table}' exists")
        else:
            print(f"✗ Table '{table}' missing")
            conn.close()
            return False
    
    # Check if admin user exists
    cursor.execute("SELECT username FROM users WHERE role='admin'")
    admin = cursor.fetchone()
    if admin:
        print(f"✓ Admin user '{admin[0]}' exists")
    else:
        print("✗ Admin user not found")
        conn.close()
        return False
    
    conn.close()
    print("Database creation test: PASSED\n")
    return True

def test_user_authentication():
    """Test user authentication"""
    print("Testing user authentication...")
    
    # Test valid login
    user = db_manager.authenticate_user("admin", "admin123")
    if user and user['username'] == 'admin':
        print("✓ Admin authentication successful")
    else:
        print("✗ Admin authentication failed")
        return False
    
    # Test invalid login
    user = db_manager.authenticate_user("admin", "wrongpassword")
    if user is None:
        print("✓ Invalid password correctly rejected")
    else:
        print("✗ Invalid password incorrectly accepted")
        return False
    
    # Test non-existent user
    user = db_manager.authenticate_user("nonexistent", "password")
    if user is None:
        print("✓ Non-existent user correctly rejected")
    else:
        print("✗ Non-existent user incorrectly accepted")
        return False
    
    print("User authentication test: PASSED\n")
    return True

def test_user_creation():
    """Test user creation functionality"""
    print("Testing user creation...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Test teacher creation
    try:
        password_hash = db_manager.hash_password("teacher123")
        cursor.execute('''
            INSERT INTO users (username, password_hash, role, full_name, email)
            VALUES (?, ?, ?, ?, ?)
        ''', ("testteacher", password_hash, "teacher", "Test Teacher", "<EMAIL>"))
        
        user_id = cursor.lastrowid
        
        cursor.execute('''
            INSERT INTO teachers (user_id, teacher_id, department, qualification)
            VALUES (?, ?, ?, ?)
        ''', (user_id, "T001", "Mathematics", "M.Sc Mathematics"))
        
        conn.commit()
        print("✓ Teacher created successfully")
        
    except Exception as e:
        print(f"✗ Teacher creation failed: {e}")
        conn.close()
        return False
    
    # Test student creation
    try:
        password_hash = db_manager.hash_password("student123")
        cursor.execute('''
            INSERT INTO users (username, password_hash, role, full_name, email)
            VALUES (?, ?, ?, ?, ?)
        ''', ("teststudent", password_hash, "student", "Test Student", "<EMAIL>"))
        
        user_id = cursor.lastrowid
        
        cursor.execute('''
            INSERT INTO students (user_id, student_id, class_name, section, roll_number)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, "S001", "Grade 10", "A", 1))
        
        conn.commit()
        print("✓ Student created successfully")
        
    except Exception as e:
        print(f"✗ Student creation failed: {e}")
        conn.close()
        return False
    
    conn.close()
    print("User creation test: PASSED\n")
    return True

def test_subject_management():
    """Test subject management"""
    print("Testing subject management...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Get teacher ID
    cursor.execute("SELECT id FROM teachers WHERE teacher_id = 'T001'")
    teacher_id = cursor.fetchone()[0]
    
    try:
        # Create test subjects
        subjects = [
            ("MATH101", "Mathematics", "Grade 10", 100, 40, teacher_id),
            ("ENG101", "English", "Grade 10", 100, 40, teacher_id),
            ("SCI101", "Science", "Grade 10", 100, 40, teacher_id)
        ]
        
        for subject in subjects:
            cursor.execute('''
                INSERT INTO subjects (subject_code, subject_name, class_name, max_marks, pass_marks, teacher_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', subject)
        
        conn.commit()
        print("✓ Subjects created successfully")
        
    except Exception as e:
        print(f"✗ Subject creation failed: {e}")
        conn.close()
        return False
    
    conn.close()
    print("Subject management test: PASSED\n")
    return True

def test_exam_management():
    """Test exam management"""
    print("Testing exam management...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Get admin user ID
    cursor.execute("SELECT id FROM users WHERE username = 'admin'")
    admin_id = cursor.fetchone()[0]
    
    try:
        # Create test exam
        cursor.execute('''
            INSERT INTO exams (exam_name, exam_type, class_name, exam_date, created_by)
            VALUES (?, ?, ?, ?, ?)
        ''', ("Mid Term Exam", "Mid Term", "Grade 10", "2024-03-15", admin_id))
        
        conn.commit()
        print("✓ Exam created successfully")
        
    except Exception as e:
        print(f"✗ Exam creation failed: {e}")
        conn.close()
        return False
    
    conn.close()
    print("Exam management test: PASSED\n")
    return True

def test_result_management():
    """Test result management"""
    print("Testing result management...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Get required IDs
    cursor.execute("SELECT id FROM students WHERE student_id = 'S001'")
    student_id = cursor.fetchone()[0]
    
    cursor.execute("SELECT id FROM subjects WHERE subject_code = 'MATH101'")
    subject_id = cursor.fetchone()[0]
    
    cursor.execute("SELECT id FROM exams WHERE exam_name = 'Mid Term Exam'")
    exam_id = cursor.fetchone()[0]
    
    cursor.execute("SELECT id FROM users WHERE username = 'testteacher'")
    teacher_id = cursor.fetchone()[0]
    
    try:
        # Create test result
        marks = 85
        grade = db_manager.calculate_grade(marks, 100)
        
        cursor.execute('''
            INSERT INTO results (student_id, subject_id, exam_id, marks_obtained, grade, entered_by)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (student_id, subject_id, exam_id, marks, grade, teacher_id))
        
        conn.commit()
        print("✓ Result created successfully")
        print(f"✓ Grade calculation working: {marks}/100 = {grade}")
        
    except Exception as e:
        print(f"✗ Result creation failed: {e}")
        conn.close()
        return False
    
    conn.close()
    print("Result management test: PASSED\n")
    return True

def test_fee_management():
    """Test fee management"""
    print("Testing fee management...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    try:
        # Create fee structure
        cursor.execute('''
            INSERT INTO fee_structure (class_name, fee_type, amount, due_date_day, academic_year)
            VALUES (?, ?, ?, ?, ?)
        ''', ("Grade 10", "Tuition Fee", 1000.00, 10, "2024-2025"))
        
        fee_structure_id = cursor.lastrowid
        
        # Get student ID
        cursor.execute("SELECT id FROM students WHERE student_id = 'S001'")
        student_id = cursor.fetchone()[0]
        
        # Get admin ID
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        admin_id = cursor.fetchone()[0]
        
        # Create payment record
        cursor.execute('''
            INSERT INTO fee_payments (student_id, fee_structure_id, amount_paid, payment_method, recorded_by)
            VALUES (?, ?, ?, ?, ?)
        ''', (student_id, fee_structure_id, 500.00, "cash", admin_id))
        
        conn.commit()
        print("✓ Fee structure created successfully")
        print("✓ Payment recorded successfully")
        
    except Exception as e:
        print(f"✗ Fee management failed: {e}")
        conn.close()
        return False
    
    conn.close()
    print("Fee management test: PASSED\n")
    return True

def test_data_integrity():
    """Test data integrity and relationships"""
    print("Testing data integrity...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Test foreign key relationships
    try:
        # Check student-user relationship
        cursor.execute('''
            SELECT COUNT(*) FROM students s
            JOIN users u ON s.user_id = u.id
            WHERE u.role = 'student'
        ''')
        student_count = cursor.fetchone()[0]
        print(f"✓ Student-User relationship: {student_count} students linked")
        
        # Check teacher-user relationship
        cursor.execute('''
            SELECT COUNT(*) FROM teachers t
            JOIN users u ON t.user_id = u.id
            WHERE u.role = 'teacher'
        ''')
        teacher_count = cursor.fetchone()[0]
        print(f"✓ Teacher-User relationship: {teacher_count} teachers linked")
        
        # Check result relationships
        cursor.execute('''
            SELECT COUNT(*) FROM results r
            JOIN students s ON r.student_id = s.id
            JOIN subjects sub ON r.subject_id = sub.id
            JOIN exams e ON r.exam_id = e.id
        ''')
        result_count = cursor.fetchone()[0]
        print(f"✓ Result relationships: {result_count} results with valid links")
        
    except Exception as e:
        print(f"✗ Data integrity check failed: {e}")
        conn.close()
        return False
    
    conn.close()
    print("Data integrity test: PASSED\n")
    return True

def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("STUDENT RESULT MANAGEMENT SYSTEM - COMPREHENSIVE TESTING")
    print("=" * 60)
    print()
    
    tests = [
        test_database_creation,
        test_user_authentication,
        test_user_creation,
        test_subject_management,
        test_exam_management,
        test_result_management,
        test_fee_management,
        test_data_integrity
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print("Test failed! Stopping execution.")
            break
    
    print("=" * 60)
    print(f"TEST SUMMARY: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is ready for use.")
        print("\nDefault Login Credentials:")
        print("Username: admin")
        print("Password: admin123")
        print("\nTest Users Created:")
        print("Teacher - Username: testteacher, Password: teacher123")
        print("Student - Username: teststudent, Password: student123")
    else:
        print("❌ Some tests failed. Please check the system.")
    
    print("=" * 60)
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
