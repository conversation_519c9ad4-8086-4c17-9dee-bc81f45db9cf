import tkinter as tk
from tkinter import ttk, messagebox
from database import db_manager
from datetime import datetime, date

class ResultManagement:
    def __init__(self, main_app):
        self.main_app = main_app
    
    def show_result_management(self):
        """Show result management interface for admin"""
        self.main_app.clear_content()
        
        # Title
        title_label = ttk.Label(self.main_app.content_frame, text="Result Management", style='Title.TLabel')
        title_label.pack(pady=10)
        
        # Notebook for tabs
        notebook = ttk.Notebook(self.main_app.content_frame)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs
        self.subjects_tab = ttk.Frame(notebook)
        self.exams_tab = ttk.Frame(notebook)
        self.results_tab = ttk.Frame(notebook)
        
        notebook.add(self.subjects_tab, text="Subjects")
        notebook.add(self.exams_tab, text="Exams")
        notebook.add(self.results_tab, text="Results")
        
        # Setup tabs
        self.setup_subjects_tab()
        self.setup_exams_tab()
        self.setup_results_tab()
    
    def setup_subjects_tab(self):
        """Setup subjects management tab"""
        # Create subject form
        form_frame = ttk.LabelFrame(self.subjects_tab, text="Add New Subject", padding=20)
        form_frame.pack(fill='x', padx=10, pady=10)
        
        # Form fields
        fields = [
            ("Subject Code:", "subject_code"),
            ("Subject Name:", "subject_name"),
            ("Class:", "class_name"),
            ("Max Marks:", "max_marks"),
            ("Pass Marks:", "pass_marks")
        ]
        
        self.subject_entries = {}
        
        for i, (label, field) in enumerate(fields):
            ttk.Label(form_frame, text=label).grid(row=i, column=0, sticky='e', padx=5, pady=5)
            entry = ttk.Entry(form_frame, width=30)
            entry.grid(row=i, column=1, padx=5, pady=5, sticky='w')
            self.subject_entries[field] = entry
        
        # Teacher selection
        ttk.Label(form_frame, text="Teacher:").grid(row=len(fields), column=0, sticky='e', padx=5, pady=5)
        self.teacher_var = tk.StringVar()
        self.teacher_combo = ttk.Combobox(form_frame, textvariable=self.teacher_var, state="readonly", width=27)
        self.teacher_combo.grid(row=len(fields), column=1, padx=5, pady=5, sticky='w')
        
        # Load teachers
        self.load_teachers()
        
        # Add button
        add_btn = ttk.Button(form_frame, text="Add Subject", command=self.add_subject)
        add_btn.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)
        
        # Subjects list
        list_frame = ttk.Frame(self.subjects_tab)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for subjects
        columns = ("ID", "Code", "Name", "Class", "Max Marks", "Pass Marks", "Teacher")
        self.subjects_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.subjects_tree.heading(col, text=col)
            self.subjects_tree.column(col, width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.subjects_tree.yview)
        self.subjects_tree.configure(yscrollcommand=scrollbar.set)
        
        self.subjects_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Load subjects
        self.refresh_subjects()
    
    def load_teachers(self):
        """Load teachers for dropdown"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT t.id, u.full_name, t.teacher_id
            FROM teachers t
            JOIN users u ON t.user_id = u.id
            WHERE u.is_active = 1
            ORDER BY u.full_name
        ''')
        
        teachers = cursor.fetchall()
        conn.close()
        
        teacher_list = [f"{teacher[1]} ({teacher[2]})" for teacher in teachers]
        self.teacher_combo['values'] = teacher_list
        self.teacher_data = {f"{teacher[1]} ({teacher[2]})": teacher[0] for teacher in teachers}
    
    def add_subject(self):
        """Add new subject"""
        try:
            # Get form data
            subject_code = self.subject_entries["subject_code"].get().strip()
            subject_name = self.subject_entries["subject_name"].get().strip()
            class_name = self.subject_entries["class_name"].get().strip()
            max_marks = self.subject_entries["max_marks"].get().strip()
            pass_marks = self.subject_entries["pass_marks"].get().strip()
            teacher_selection = self.teacher_var.get()
            
            # Validation
            if not all([subject_code, subject_name, class_name, max_marks, pass_marks]):
                messagebox.showerror("Error", "Please fill in all fields")
                return
            
            try:
                max_marks = int(max_marks)
                pass_marks = int(pass_marks)
            except ValueError:
                messagebox.showerror("Error", "Marks must be numbers")
                return
            
            teacher_id = self.teacher_data.get(teacher_selection) if teacher_selection else None
            
            # Insert subject
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO subjects (subject_code, subject_name, class_name, max_marks, pass_marks, teacher_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (subject_code, subject_name, class_name, max_marks, pass_marks, teacher_id))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("Success", "Subject added successfully!")
            
            # Clear form
            for entry in self.subject_entries.values():
                entry.delete(0, tk.END)
            self.teacher_var.set("")
            
            # Refresh list
            self.refresh_subjects()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add subject: {str(e)}")
    
    def refresh_subjects(self):
        """Refresh subjects list"""
        # Clear existing items
        for item in self.subjects_tree.get_children():
            self.subjects_tree.delete(item)
        
        # Load subjects
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT s.id, s.subject_code, s.subject_name, s.class_name, 
                   s.max_marks, s.pass_marks, u.full_name
            FROM subjects s
            LEFT JOIN teachers t ON s.teacher_id = t.id
            LEFT JOIN users u ON t.user_id = u.id
            ORDER BY s.class_name, s.subject_name
        ''')
        
        subjects = cursor.fetchall()
        conn.close()
        
        for subject in subjects:
            teacher_name = subject[6] if subject[6] else "Not Assigned"
            self.subjects_tree.insert('', 'end', values=subject[:6] + (teacher_name,))
    
    def setup_exams_tab(self):
        """Setup exams management tab"""
        # Create exam form
        form_frame = ttk.LabelFrame(self.exams_tab, text="Create New Exam", padding=20)
        form_frame.pack(fill='x', padx=10, pady=10)
        
        # Form fields
        fields = [
            ("Exam Name:", "exam_name"),
            ("Exam Type:", "exam_type"),
            ("Class:", "class_name"),
            ("Exam Date:", "exam_date")
        ]
        
        self.exam_entries = {}
        
        for i, (label, field) in enumerate(fields):
            ttk.Label(form_frame, text=label).grid(row=i, column=0, sticky='e', padx=5, pady=5)
            
            if field == "exam_type":
                entry = ttk.Combobox(form_frame, values=["Mid Term", "Final Term", "Monthly Test", "Quiz"], 
                                   state="readonly", width=27)
            elif field == "exam_date":
                entry = ttk.Entry(form_frame, width=30)
                ttk.Label(form_frame, text="(YYYY-MM-DD)", font=('Arial', 8)).grid(row=i, column=2, sticky='w')
            else:
                entry = ttk.Entry(form_frame, width=30)
            
            entry.grid(row=i, column=1, padx=5, pady=5, sticky='w')
            self.exam_entries[field] = entry
        
        # Add button
        add_exam_btn = ttk.Button(form_frame, text="Create Exam", command=self.create_exam)
        add_exam_btn.grid(row=len(fields), column=0, columnspan=2, pady=20)
        
        # Exams list
        list_frame = ttk.Frame(self.exams_tab)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for exams
        columns = ("ID", "Name", "Type", "Class", "Date", "Created By")
        self.exams_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.exams_tree.heading(col, text=col)
            self.exams_tree.column(col, width=120)
        
        # Scrollbar
        scrollbar2 = ttk.Scrollbar(list_frame, orient='vertical', command=self.exams_tree.yview)
        self.exams_tree.configure(yscrollcommand=scrollbar2.set)
        
        self.exams_tree.pack(side='left', fill='both', expand=True)
        scrollbar2.pack(side='right', fill='y')
        
        # Load exams
        self.refresh_exams()
    
    def create_exam(self):
        """Create new exam"""
        try:
            # Get form data
            exam_name = self.exam_entries["exam_name"].get().strip()
            exam_type = self.exam_entries["exam_type"].get().strip()
            class_name = self.exam_entries["class_name"].get().strip()
            exam_date = self.exam_entries["exam_date"].get().strip()
            
            # Validation
            if not all([exam_name, exam_type, class_name]):
                messagebox.showerror("Error", "Please fill in all required fields")
                return
            
            # Insert exam
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO exams (exam_name, exam_type, class_name, exam_date, created_by)
                VALUES (?, ?, ?, ?, ?)
            ''', (exam_name, exam_type, class_name, exam_date if exam_date else None, 
                  self.main_app.current_user['id']))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("Success", "Exam created successfully!")
            
            # Clear form
            for entry in self.exam_entries.values():
                if hasattr(entry, 'delete'):
                    entry.delete(0, tk.END)
                else:
                    entry.set("")
            
            # Refresh list
            self.refresh_exams()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create exam: {str(e)}")
    
    def refresh_exams(self):
        """Refresh exams list"""
        # Clear existing items
        for item in self.exams_tree.get_children():
            self.exams_tree.delete(item)
        
        # Load exams
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT e.id, e.exam_name, e.exam_type, e.class_name, e.exam_date, u.full_name
            FROM exams e
            LEFT JOIN users u ON e.created_by = u.id
            ORDER BY e.exam_date DESC, e.class_name
        ''')
        
        exams = cursor.fetchall()
        conn.close()
        
        for exam in exams:
            self.exams_tree.insert('', 'end', values=exam)
    
    def setup_results_tab(self):
        """Setup results management tab"""
        # Filter frame
        filter_frame = ttk.LabelFrame(self.results_tab, text="Filter Results", padding=10)
        filter_frame.pack(fill='x', padx=10, pady=10)
        
        # Filter controls
        ttk.Label(filter_frame, text="Class:").grid(row=0, column=0, padx=5, pady=5)
        self.filter_class_var = tk.StringVar()
        class_combo = ttk.Combobox(filter_frame, textvariable=self.filter_class_var, width=15)
        class_combo.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(filter_frame, text="Exam:").grid(row=0, column=2, padx=5, pady=5)
        self.filter_exam_var = tk.StringVar()
        exam_combo = ttk.Combobox(filter_frame, textvariable=self.filter_exam_var, width=20)
        exam_combo.grid(row=0, column=3, padx=5, pady=5)
        
        filter_btn = ttk.Button(filter_frame, text="Filter", command=self.filter_results)
        filter_btn.grid(row=0, column=4, padx=10, pady=5)
        
        # Results list
        list_frame = ttk.Frame(self.results_tab)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for results
        columns = ("Student", "Subject", "Exam", "Marks", "Grade", "Entered By", "Date")
        self.results_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=120)
        
        # Scrollbar
        scrollbar3 = ttk.Scrollbar(list_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar3.set)
        
        self.results_tree.pack(side='left', fill='both', expand=True)
        scrollbar3.pack(side='right', fill='y')
        
        # Load filter options and results
        self.load_filter_options()
        self.refresh_results()
    
    def load_filter_options(self):
        """Load filter options"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # Load classes
        cursor.execute("SELECT DISTINCT class_name FROM students ORDER BY class_name")
        classes = [row[0] for row in cursor.fetchall()]
        
        # Load exams
        cursor.execute("SELECT id, exam_name, class_name FROM exams ORDER BY exam_name")
        exams = cursor.fetchall()
        
        conn.close()
        
        # Update comboboxes
        self.filter_class_var.set("")
        self.filter_exam_var.set("")
        
        # Find comboboxes and update values
        for widget in self.results_tab.winfo_children():
            if isinstance(widget, ttk.LabelFrame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Combobox):
                        if child.cget('textvariable') == str(self.filter_class_var):
                            child['values'] = classes
                        elif child.cget('textvariable') == str(self.filter_exam_var):
                            exam_list = [f"{exam[1]} ({exam[2]})" for exam in exams]
                            child['values'] = exam_list
                            self.exam_data = {f"{exam[1]} ({exam[2]})": exam[0] for exam in exams}
    
    def filter_results(self):
        """Filter results based on selection"""
        self.refresh_results()
    
    def refresh_results(self):
        """Refresh results list"""
        # Clear existing items
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # Build query with filters
        query = '''
            SELECT u.full_name, sub.subject_name, e.exam_name, r.marks_obtained, 
                   r.grade, u2.full_name, r.entered_at
            FROM results r
            JOIN students s ON r.student_id = s.id
            JOIN users u ON s.user_id = u.id
            JOIN subjects sub ON r.subject_id = sub.id
            JOIN exams e ON r.exam_id = e.id
            LEFT JOIN users u2 ON r.entered_by = u2.id
            WHERE 1=1
        '''
        
        params = []
        
        # Apply filters
        if hasattr(self, 'filter_class_var') and self.filter_class_var.get():
            query += " AND s.class_name = ?"
            params.append(self.filter_class_var.get())
        
        if hasattr(self, 'filter_exam_var') and self.filter_exam_var.get():
            exam_selection = self.filter_exam_var.get()
            if hasattr(self, 'exam_data') and exam_selection in self.exam_data:
                query += " AND r.exam_id = ?"
                params.append(self.exam_data[exam_selection])
        
        query += " ORDER BY u.full_name, sub.subject_name"
        
        # Load results
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        conn.close()
        
        for result in results:
            # Format date
            entered_date = result[6][:10] if result[6] else ""
            self.results_tree.insert('', 'end', values=result[:6] + (entered_date,))
    
    def show_result_entry(self):
        """Show result entry interface for teachers"""
        self.main_app.clear_content()

        title_label = ttk.Label(self.main_app.content_frame, text="Result Entry", style='Title.TLabel')
        title_label.pack(pady=10)

        # Selection frame
        selection_frame = ttk.LabelFrame(self.main_app.content_frame, text="Select Exam and Subject", padding=20)
        selection_frame.pack(fill='x', padx=10, pady=10)

        # Exam selection
        ttk.Label(selection_frame, text="Exam:").grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.entry_exam_var = tk.StringVar()
        exam_combo = ttk.Combobox(selection_frame, textvariable=self.entry_exam_var, state="readonly", width=30)
        exam_combo.grid(row=0, column=1, padx=5, pady=5)
        exam_combo.bind('<<ComboboxSelected>>', self.load_subjects_for_entry)

        # Subject selection
        ttk.Label(selection_frame, text="Subject:").grid(row=1, column=0, sticky='e', padx=5, pady=5)
        self.entry_subject_var = tk.StringVar()
        self.subject_combo = ttk.Combobox(selection_frame, textvariable=self.entry_subject_var,
                                         state="readonly", width=30)
        self.subject_combo.grid(row=1, column=1, padx=5, pady=5)
        self.subject_combo.bind('<<ComboboxSelected>>', self.load_students_for_entry)

        # Load button
        load_btn = ttk.Button(selection_frame, text="Load Students", command=self.load_students_for_entry)
        load_btn.grid(row=2, column=0, columnspan=2, pady=20)

        # Students frame
        self.students_frame = ttk.LabelFrame(self.main_app.content_frame, text="Enter Marks", padding=10)
        self.students_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Load exams
        self.load_exams_for_entry()

        # Save button
        save_frame = ttk.Frame(self.main_app.content_frame)
        save_frame.pack(fill='x', padx=10, pady=5)

        save_btn = ttk.Button(save_frame, text="Save Results", command=self.save_results)
        save_btn.pack(side='right', padx=10)

    def load_exams_for_entry(self):
        """Load exams for result entry"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, exam_name, class_name, exam_type
            FROM exams
            ORDER BY exam_date DESC, exam_name
        ''')

        exams = cursor.fetchall()
        conn.close()

        exam_list = [f"{exam[1]} ({exam[2]}) - {exam[3]}" for exam in exams]

        # Find the exam combobox and update it
        for widget in self.main_app.content_frame.winfo_children():
            if isinstance(widget, ttk.LabelFrame) and "Select Exam" in widget.cget('text'):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Combobox) and child.cget('textvariable') == str(self.entry_exam_var):
                        child['values'] = exam_list
                        break

        self.exam_data_entry = {f"{exam[1]} ({exam[2]}) - {exam[3]}": exam for exam in exams}

    def load_subjects_for_entry(self, event=None):
        """Load subjects based on selected exam"""
        exam_selection = self.entry_exam_var.get()
        if not exam_selection or exam_selection not in self.exam_data_entry:
            return

        exam = self.exam_data_entry[exam_selection]
        class_name = exam[2]

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, subject_name, subject_code, max_marks
            FROM subjects
            WHERE class_name = ?
            ORDER BY subject_name
        ''', (class_name,))

        subjects = cursor.fetchall()
        conn.close()

        subject_list = [f"{subject[1]} ({subject[2]})" for subject in subjects]
        self.subject_combo['values'] = subject_list
        self.subject_data_entry = {f"{subject[1]} ({subject[2]})": subject for subject in subjects}

    def load_students_for_entry(self, event=None):
        """Load students for result entry"""
        exam_selection = self.entry_exam_var.get()
        subject_selection = self.entry_subject_var.get()

        if not exam_selection or not subject_selection:
            return

        if exam_selection not in self.exam_data_entry or subject_selection not in self.subject_data_entry:
            return

        exam = self.exam_data_entry[exam_selection]
        subject = self.subject_data_entry[subject_selection]
        class_name = exam[2]

        # Clear students frame
        for widget in self.students_frame.winfo_children():
            widget.destroy()

        # Load students
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT s.id, u.full_name, s.student_id, s.roll_number
            FROM students s
            JOIN users u ON s.user_id = u.id
            WHERE s.class_name = ? AND u.is_active = 1
            ORDER BY s.roll_number, u.full_name
        ''', (class_name,))

        students = cursor.fetchall()

        # Check for existing results
        cursor.execute('''
            SELECT student_id, marks_obtained
            FROM results
            WHERE exam_id = ? AND subject_id = ?
        ''', (exam[0], subject[0]))

        existing_results = {row[0]: row[1] for row in cursor.fetchall()}
        conn.close()

        # Create headers
        ttk.Label(self.students_frame, text="Roll No", font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=5, pady=5)
        ttk.Label(self.students_frame, text="Student Name", font=('Arial', 10, 'bold')).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(self.students_frame, text="Student ID", font=('Arial', 10, 'bold')).grid(row=0, column=2, padx=5, pady=5)
        ttk.Label(self.students_frame, text=f"Marks (Max: {subject[3]})", font=('Arial', 10, 'bold')).grid(row=0, column=3, padx=5, pady=5)
        ttk.Label(self.students_frame, text="Grade", font=('Arial', 10, 'bold')).grid(row=0, column=4, padx=5, pady=5)

        # Create entry fields for each student
        self.student_entries = {}

        for i, student in enumerate(students, start=1):
            # Roll number
            ttk.Label(self.students_frame, text=str(student[3] or "")).grid(row=i, column=0, padx=5, pady=2)

            # Student name
            ttk.Label(self.students_frame, text=student[1]).grid(row=i, column=1, padx=5, pady=2, sticky='w')

            # Student ID
            ttk.Label(self.students_frame, text=student[2]).grid(row=i, column=2, padx=5, pady=2)

            # Marks entry
            marks_entry = ttk.Entry(self.students_frame, width=10)
            marks_entry.grid(row=i, column=3, padx=5, pady=2)

            # Pre-fill existing marks
            if student[0] in existing_results:
                marks_entry.insert(0, str(existing_results[student[0]]))

            # Grade label
            grade_label = ttk.Label(self.students_frame, text="", width=5)
            grade_label.grid(row=i, column=4, padx=5, pady=2)

            # Bind marks entry to calculate grade
            marks_entry.bind('<KeyRelease>', lambda e, lbl=grade_label, max_marks=subject[3]:
                           self.calculate_grade_display(e, lbl, max_marks))

            self.student_entries[student[0]] = {
                'marks_entry': marks_entry,
                'grade_label': grade_label,
                'student_data': student
            }

    def calculate_grade_display(self, event, grade_label, max_marks):
        """Calculate and display grade as user types"""
        try:
            marks = float(event.widget.get())
            if 0 <= marks <= max_marks:
                grade = db_manager.calculate_grade(marks, max_marks)
                grade_label.config(text=grade)
            else:
                grade_label.config(text="Invalid")
        except ValueError:
            grade_label.config(text="")

    def save_results(self):
        """Save all entered results"""
        if not hasattr(self, 'student_entries') or not self.student_entries:
            messagebox.showwarning("Warning", "No students loaded for result entry")
            return

        exam_selection = self.entry_exam_var.get()
        subject_selection = self.entry_subject_var.get()

        if not exam_selection or not subject_selection:
            messagebox.showerror("Error", "Please select exam and subject")
            return

        exam = self.exam_data_entry[exam_selection]
        subject = self.subject_data_entry[subject_selection]

        # Validate and collect results
        results_to_save = []
        errors = []

        for student_id, entry_data in self.student_entries.items():
            marks_text = entry_data['marks_entry'].get().strip()
            if marks_text:  # Only process if marks are entered
                try:
                    marks = float(marks_text)
                    if 0 <= marks <= subject[3]:  # max_marks
                        grade = db_manager.calculate_grade(marks, subject[3])
                        results_to_save.append((student_id, subject[0], exam[0], marks, grade))
                    else:
                        student_name = entry_data['student_data'][1]
                        errors.append(f"{student_name}: Marks must be between 0 and {subject[3]}")
                except ValueError:
                    student_name = entry_data['student_data'][1]
                    errors.append(f"{student_name}: Invalid marks format")

        if errors:
            messagebox.showerror("Validation Errors", "\n".join(errors))
            return

        if not results_to_save:
            messagebox.showwarning("Warning", "No valid marks entered")
            return

        # Save results to database
        try:
            conn = db_manager.get_connection()
            cursor = conn.cursor()

            for student_id, subject_id, exam_id, marks, grade in results_to_save:
                cursor.execute('''
                    INSERT OR REPLACE INTO results
                    (student_id, subject_id, exam_id, marks_obtained, grade, entered_by, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (student_id, subject_id, exam_id, marks, grade, self.main_app.current_user['id']))

            conn.commit()
            conn.close()

            messagebox.showinfo("Success", f"Results saved successfully for {len(results_to_save)} students!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save results: {str(e)}")
    
    def show_my_students(self):
        """Show students assigned to current teacher"""
        self.main_app.clear_content()
        
        title_label = ttk.Label(self.main_app.content_frame, text="My Students", style='Title.TLabel')
        title_label.pack(pady=10)
        
        info_label = ttk.Label(self.main_app.content_frame, 
                              text="Students list for current teacher will be implemented here.")
        info_label.pack(pady=20)
    
    def show_my_results(self):
        """Show results for current student"""
        self.main_app.clear_content()

        title_label = ttk.Label(self.main_app.content_frame, text="My Results", style='Title.TLabel')
        title_label.pack(pady=10)

        # Get current student data
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT s.id, s.student_id, s.class_name, u.full_name
            FROM students s
            JOIN users u ON s.user_id = u.id
            WHERE u.id = ?
        ''', (self.main_app.current_user['id'],))

        student_data = cursor.fetchone()

        if not student_data:
            error_label = ttk.Label(self.main_app.content_frame,
                                  text="Student profile not found. Please contact administrator.")
            error_label.pack(pady=20)
            conn.close()
            return

        # Student info
        info_frame = ttk.LabelFrame(self.main_app.content_frame, text="Student Information", padding=10)
        info_frame.pack(fill='x', padx=10, pady=10)

        ttk.Label(info_frame, text=f"Name: {student_data[3]}", font=('Arial', 10)).pack(anchor='w')
        ttk.Label(info_frame, text=f"Student ID: {student_data[1]}", font=('Arial', 10)).pack(anchor='w')
        ttk.Label(info_frame, text=f"Class: {student_data[2]}", font=('Arial', 10)).pack(anchor='w')

        # Results by exam
        cursor.execute('''
            SELECT DISTINCT e.id, e.exam_name, e.exam_type, e.exam_date
            FROM results r
            JOIN exams e ON r.exam_id = e.id
            WHERE r.student_id = ?
            ORDER BY e.exam_date DESC, e.exam_name
        ''', (student_data[0],))

        exams = cursor.fetchall()

        if not exams:
            no_results_label = ttk.Label(self.main_app.content_frame,
                                       text="No results found.")
            no_results_label.pack(pady=20)
            conn.close()
            return

        # Create notebook for different exams
        results_notebook = ttk.Notebook(self.main_app.content_frame)
        results_notebook.pack(fill='both', expand=True, padx=10, pady=10)

        for exam in exams:
            exam_frame = ttk.Frame(results_notebook)
            results_notebook.add(exam_frame, text=f"{exam[1]} ({exam[2]})")

            # Get results for this exam
            cursor.execute('''
                SELECT sub.subject_name, r.marks_obtained, sub.max_marks, r.grade,
                       sub.pass_marks, r.entered_at
                FROM results r
                JOIN subjects sub ON r.subject_id = sub.id
                WHERE r.student_id = ? AND r.exam_id = ?
                ORDER BY sub.subject_name
            ''', (student_data[0], exam[0]))

            exam_results = cursor.fetchall()

            # Results table
            columns = ("Subject", "Marks Obtained", "Max Marks", "Grade", "Status", "Date Entered")
            results_tree = ttk.Treeview(exam_frame, columns=columns, show='headings', height=10)

            for col in columns:
                results_tree.heading(col, text=col)
                results_tree.column(col, width=120)

            # Scrollbar
            scrollbar = ttk.Scrollbar(exam_frame, orient='vertical', command=results_tree.yview)
            results_tree.configure(yscrollcommand=scrollbar.set)

            results_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
            scrollbar.pack(side='right', fill='y', pady=10)

            # Populate results
            total_marks = 0
            total_max_marks = 0

            for result in exam_results:
                status = "Pass" if result[1] >= result[4] else "Fail"  # marks >= pass_marks
                date_entered = result[5][:10] if result[5] else ""

                results_tree.insert('', 'end', values=(
                    result[0], result[1], result[2], result[3], status, date_entered
                ))

                total_marks += result[1]
                total_max_marks += result[2]

            # Summary frame
            summary_frame = ttk.Frame(exam_frame)
            summary_frame.pack(fill='x', padx=10, pady=5)

            if total_max_marks > 0:
                percentage = (total_marks / total_max_marks) * 100
                overall_grade = db_manager.calculate_grade(total_marks, total_max_marks)

                ttk.Label(summary_frame, text=f"Total: {total_marks}/{total_max_marks}",
                         font=('Arial', 10, 'bold')).pack(side='left', padx=10)
                ttk.Label(summary_frame, text=f"Percentage: {percentage:.2f}%",
                         font=('Arial', 10, 'bold')).pack(side='left', padx=10)
                ttk.Label(summary_frame, text=f"Overall Grade: {overall_grade}",
                         font=('Arial', 10, 'bold')).pack(side='left', padx=10)

        conn.close()
