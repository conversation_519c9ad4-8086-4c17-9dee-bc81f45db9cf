@echo off
title Student Result Management System
echo.
echo ========================================
echo  Student Result Management System
echo ========================================
echo.

REM Try different Python commands
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting with 'python'...
    python run.py
    goto :end
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting with 'py'...
    py run.py
    goto :end
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting with 'python3'...
    python3 run.py
    goto :end
)

echo.
echo ERROR: Python not found!
echo.
echo Please install Python 3.7+ from https://python.org
echo Make sure to check "Add Python to PATH" during installation.
echo.

:end
echo.
echo Press any key to exit...
pause >nul
