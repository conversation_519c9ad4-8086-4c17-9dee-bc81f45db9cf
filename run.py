#!/usr/bin/env python3
"""
Student Result Management System Launcher
This script provides a simple way to start the application with proper error handling.
"""

import sys
import os
import subprocess

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Error: Python 3.7 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    return True

def check_tkinter():
    """Check if tkinter is available"""
    try:
        import tkinter
        return True
    except ImportError:
        print("❌ Error: tkinter is not available.")
        print("Please install tkinter or use a Python distribution that includes it.")
        return False

def run_tests():
    """Ask user if they want to run tests first"""
    response = input("Would you like to run system tests first? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        print("\n" + "="*50)
        print("Running system tests...")
        print("="*50)
        
        try:
            result = subprocess.run([sys.executable, 'test_system.py'], 
                                  capture_output=False, text=True)
            if result.returncode == 0:
                print("\n✅ All tests passed! Starting application...")
                return True
            else:
                print("\n❌ Some tests failed. Do you want to continue anyway? (y/n): ", end="")
                response = input().lower().strip()
                return response in ['y', 'yes']
        except Exception as e:
            print(f"❌ Error running tests: {e}")
            print("Do you want to continue anyway? (y/n): ", end="")
            response = input().lower().strip()
            return response in ['y', 'yes']
    return True

def main():
    """Main launcher function"""
    print("🎓 Student Result Management System")
    print("="*50)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return
    
    # Check tkinter availability
    if not check_tkinter():
        input("Press Enter to exit...")
        return
    
    print("✅ Python version compatible")
    print("✅ Required modules available")
    
    # Ask about running tests
    if not run_tests():
        print("Exiting...")
        return
    
    # Start the application
    print("\n🚀 Starting Student Result Management System...")
    print("="*50)
    
    try:
        # Import and run the main application
        from main import StudentManagementSystem
        app = StudentManagementSystem()
        app.run()
    except KeyboardInterrupt:
        print("\n👋 Application closed by user")
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure all files are in the same directory")
        print("2. Try running 'python test_system.py' to check system integrity")
        print("3. Ensure you have proper permissions to create database files")
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
