#!/usr/bin/env python3
"""
Add sample data to demonstrate the Student Result Management System
This script adds more students, results, and fee data to show the system's capabilities
"""

from database import db_manager
from datetime import datetime, date
import random

def add_sample_students():
    """Add sample students to the database"""
    print("Adding sample students...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Sample student data
    students_data = [
        ("student001", "<PERSON>", "<EMAIL>", "Grade 10", "A", 2, "<PERSON>", "555-0101"),
        ("student002", "<PERSON>", "<EMAIL>", "Grade 10", "A", 3, "<PERSON>", "555-0102"),
        ("student003", "<PERSON>", "<EMAIL>", "Grade 10", "A", 4, "<PERSON>", "555-0103"),
        ("student004", "<PERSON>", "<EMAIL>", "Grade 10", "B", 1, "<PERSON>", "555-0104"),
        ("student005", "<PERSON>", "<EMAIL>", "Grade 10", "B", 2, "<PERSON>", "555-0105"),
        ("student006", "<PERSON>", "<EMAIL>", "Grade 11", "A", 1, "<PERSON> <PERSON>", "555-0106"),
        ("student007", "<PERSON> <PERSON>", "<EMAIL>", "Grade 11", "A", 2, "<PERSON> <PERSON>", "555-0107"),
        ("student008", "<PERSON> <PERSON>", "<EMAIL>", "Grade 11", "B", 1, "Maria Garcia", "555-0108"),
    ]
    
    try:
        for i, (student_id, full_name, email, class_name, section, roll_no, guardian, guardian_phone) in enumerate(students_data):
            username = f"student{i+2:03d}"  # student002, student003, etc.
            
            # Check if user already exists
            cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
            if cursor.fetchone():
                continue
            
            # Create user
            password_hash = db_manager.hash_password("student123")
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name, email, phone)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, password_hash, "student", full_name, email, guardian_phone))
            
            user_id = cursor.lastrowid
            
            # Create student record
            cursor.execute('''
                INSERT INTO students (user_id, student_id, class_name, section, roll_number, guardian_name, guardian_phone)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, student_id, class_name, section, roll_no, guardian, guardian_phone))
        
        conn.commit()
        print("✓ Sample students added successfully")
        
    except Exception as e:
        print(f"✗ Error adding students: {e}")
        conn.rollback()
    finally:
        conn.close()

def add_sample_teachers():
    """Add sample teachers to the database"""
    print("Adding sample teachers...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Sample teacher data
    teachers_data = [
        ("teacher002", "Dr. Alice Cooper", "<EMAIL>", "English", "M.A. English Literature", 8, 55000),
        ("teacher003", "Prof. Bob Johnson", "<EMAIL>", "Science", "M.Sc. Physics", 12, 60000),
        ("teacher004", "Ms. Carol White", "<EMAIL>", "History", "M.A. History", 6, 52000),
    ]
    
    try:
        for i, (teacher_id, full_name, email, department, qualification, experience, salary) in enumerate(teachers_data):
            username = f"teacher{i+2:03d}"  # teacher002, teacher003, etc.
            
            # Check if user already exists
            cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
            if cursor.fetchone():
                continue
            
            # Create user
            password_hash = db_manager.hash_password("teacher123")
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name, email)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, password_hash, "teacher", full_name, email))
            
            user_id = cursor.lastrowid
            
            # Create teacher record
            cursor.execute('''
                INSERT INTO teachers (user_id, teacher_id, department, qualification, experience_years, salary)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, teacher_id, department, qualification, experience, salary))
        
        conn.commit()
        print("✓ Sample teachers added successfully")
        
    except Exception as e:
        print(f"✗ Error adding teachers: {e}")
        conn.rollback()
    finally:
        conn.close()

def add_sample_subjects():
    """Add more sample subjects"""
    print("Adding sample subjects...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    try:
        # Get teacher IDs
        cursor.execute("SELECT id, teacher_id FROM teachers")
        teachers = cursor.fetchall()
        
        if len(teachers) < 2:
            print("Not enough teachers to assign subjects")
            return
        
        # Sample subjects
        subjects_data = [
            ("ENG101", "English Literature", "Grade 10", 100, 40, teachers[1][0]),  # Alice Cooper
            ("SCI101", "Physics", "Grade 10", 100, 40, teachers[2][0]),  # Bob Johnson
            ("HIST101", "World History", "Grade 10", 100, 40, teachers[3][0]),  # Carol White
            ("MATH201", "Advanced Mathematics", "Grade 11", 100, 40, teachers[0][0]),  # Original teacher
            ("ENG201", "English Composition", "Grade 11", 100, 40, teachers[1][0]),  # Alice Cooper
        ]
        
        for subject_code, subject_name, class_name, max_marks, pass_marks, teacher_id in subjects_data:
            # Check if subject already exists
            cursor.execute("SELECT id FROM subjects WHERE subject_code = ?", (subject_code,))
            if cursor.fetchone():
                continue
            
            cursor.execute('''
                INSERT INTO subjects (subject_code, subject_name, class_name, max_marks, pass_marks, teacher_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (subject_code, subject_name, class_name, max_marks, pass_marks, teacher_id))
        
        conn.commit()
        print("✓ Sample subjects added successfully")
        
    except Exception as e:
        print(f"✗ Error adding subjects: {e}")
        conn.rollback()
    finally:
        conn.close()

def add_sample_results():
    """Add sample results for demonstration"""
    print("Adding sample results...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    try:
        # Get students, subjects, and exams
        cursor.execute("SELECT id FROM students")
        students = [row[0] for row in cursor.fetchall()]
        
        cursor.execute("SELECT id FROM subjects")
        subjects = [row[0] for row in cursor.fetchall()]
        
        cursor.execute("SELECT id FROM exams")
        exams = [row[0] for row in cursor.fetchall()]
        
        if not students or not subjects or not exams:
            print("Not enough data to create results")
            return
        
        # Get a teacher ID for entering results
        cursor.execute("SELECT u.id FROM users u JOIN teachers t ON u.id = t.user_id LIMIT 1")
        teacher_id = cursor.fetchone()[0]
        
        # Generate sample results
        results_added = 0
        for student_id in students[:6]:  # First 6 students
            for subject_id in subjects[:3]:  # First 3 subjects
                for exam_id in exams[:1]:  # First exam
                    # Check if result already exists
                    cursor.execute('''
                        SELECT id FROM results 
                        WHERE student_id = ? AND subject_id = ? AND exam_id = ?
                    ''', (student_id, subject_id, exam_id))
                    
                    if cursor.fetchone():
                        continue
                    
                    # Generate random marks (40-95 range for realistic results)
                    marks = random.randint(40, 95)
                    grade = db_manager.calculate_grade(marks, 100)
                    
                    cursor.execute('''
                        INSERT INTO results (student_id, subject_id, exam_id, marks_obtained, grade, entered_by)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (student_id, subject_id, exam_id, marks, grade, teacher_id))
                    
                    results_added += 1
        
        conn.commit()
        print(f"✓ {results_added} sample results added successfully")
        
    except Exception as e:
        print(f"✗ Error adding results: {e}")
        conn.rollback()
    finally:
        conn.close()

def add_sample_fees():
    """Add sample fee structures and payments"""
    print("Adding sample fee data...")
    
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    try:
        # Add fee structures for different classes
        fee_structures = [
            ("Grade 10", "Tuition Fee", 1200.00, 10, "2024-2025"),
            ("Grade 10", "Lab Fee", 200.00, 15, "2024-2025"),
            ("Grade 11", "Tuition Fee", 1400.00, 10, "2024-2025"),
            ("Grade 11", "Lab Fee", 250.00, 15, "2024-2025"),
        ]
        
        fee_structure_ids = []
        for class_name, fee_type, amount, due_day, academic_year in fee_structures:
            # Check if fee structure exists
            cursor.execute('''
                SELECT id FROM fee_structure 
                WHERE class_name = ? AND fee_type = ? AND academic_year = ?
            ''', (class_name, fee_type, academic_year))
            
            existing = cursor.fetchone()
            if existing:
                fee_structure_ids.append(existing[0])
                continue
            
            cursor.execute('''
                INSERT INTO fee_structure (class_name, fee_type, amount, due_date_day, academic_year)
                VALUES (?, ?, ?, ?, ?)
            ''', (class_name, fee_type, amount, due_day, academic_year))
            
            fee_structure_ids.append(cursor.lastrowid)
        
        # Add sample payments
        cursor.execute("SELECT id FROM students LIMIT 5")
        students = [row[0] for row in cursor.fetchall()]
        
        cursor.execute("SELECT id FROM users WHERE role = 'admin' LIMIT 1")
        admin_id = cursor.fetchone()[0]
        
        payments_added = 0
        for student_id in students:
            for fee_structure_id in fee_structure_ids[:2]:  # First 2 fee structures
                # Check if payment exists
                cursor.execute('''
                    SELECT id FROM fee_payments 
                    WHERE student_id = ? AND fee_structure_id = ?
                ''', (student_id, fee_structure_id))
                
                if cursor.fetchone():
                    continue
                
                # Get fee amount
                cursor.execute("SELECT amount FROM fee_structure WHERE id = ?", (fee_structure_id,))
                fee_amount = cursor.fetchone()[0]
                
                # Random partial or full payment
                payment_amount = random.choice([fee_amount, fee_amount * 0.5, fee_amount * 0.75])
                
                cursor.execute('''
                    INSERT INTO fee_payments (student_id, fee_structure_id, amount_paid, payment_method, recorded_by)
                    VALUES (?, ?, ?, ?, ?)
                ''', (student_id, fee_structure_id, payment_amount, "cash", admin_id))
                
                payments_added += 1
        
        conn.commit()
        print(f"✓ Fee structures and {payments_added} payments added successfully")
        
    except Exception as e:
        print(f"✗ Error adding fee data: {e}")
        conn.rollback()
    finally:
        conn.close()

def main():
    """Add all sample data"""
    print("=" * 60)
    print("ADDING SAMPLE DATA TO STUDENT MANAGEMENT SYSTEM")
    print("=" * 60)
    print()
    
    add_sample_students()
    add_sample_teachers()
    add_sample_subjects()
    add_sample_results()
    add_sample_fees()
    
    print()
    print("=" * 60)
    print("SAMPLE DATA ADDITION COMPLETE!")
    print("=" * 60)
    print()
    print("You can now:")
    print("1. Login as admin to see more users and data")
    print("2. Login as teacher to see students and results")
    print("3. Login as student to see results and fees")
    print()
    print("Additional login credentials:")
    print("- teacher002 / teacher123")
    print("- teacher003 / teacher123") 
    print("- student002 / student123")
    print("- student003 / student123")

if __name__ == "__main__":
    main()
