# Student Result Management System - User Guide

## 🚀 Quick Start

### For Windows Users
1. Double-click `start.bat` to launch the application
2. Or run `python run.py` in command prompt

### For Mac/Linux Users
1. Open terminal in the project directory
2. Run `python3 run.py` or `python run.py`

### First Time Setup
1. The system will create a database automatically
2. Default admin login: `admin` / `admin123`
3. Run tests first (recommended) to validate system

## 👥 User Roles & Capabilities

### 🔧 Administrator
**Access**: Full system control

**Main Functions**:
- Create and manage user accounts (students, teachers, staff)
- Set up subjects and assign teachers
- Create exam schedules
- Manage fee structures
- View system-wide reports and statistics
- Activate/deactivate user accounts

**Navigation Menu**:
- Home (Dashboard with statistics)
- User Management (Create users, manage accounts)
- Result Management (Subjects, exams, results overview)
- Financial Management (Fee structures, payments, reports)
- Reports (System-wide analytics)

### 👨‍🏫 Teacher
**Access**: Result entry and student management

**Main Functions**:
- Enter and update student marks
- View assigned students and subjects
- Generate result reports for their classes
- Update student results when corrections needed

**Navigation Menu**:
- Home (Teacher dashboard)
- Result Entry (Enter marks for exams)
- My Students (View assigned students)
- Reports (Class-specific reports)

### 🎓 Student
**Access**: View personal results and fee status

**Main Functions**:
- View exam results and grades
- Check fee payment status
- View payment history
- Update personal profile information

**Navigation Menu**:
- Home (Student dashboard)
- My Results (View all exam results)
- Fee Status (Payment status and history)

## 📋 Step-by-Step Workflows

### Setting Up the System (Admin)

1. **Login as Admin**
   - Username: `admin`
   - Password: `admin123`

2. **Create Teachers**
   - Go to User Management → Create User
   - Fill in teacher details
   - Select "teacher" role
   - Add teacher-specific information (ID, department, etc.)

3. **Create Students**
   - Go to User Management → Create User
   - Fill in student details
   - Select "student" role
   - Add student-specific information (ID, class, roll number, etc.)

4. **Set Up Subjects**
   - Go to Result Management → Subjects
   - Add subjects with codes, names, and max marks
   - Assign teachers to subjects

5. **Create Exams**
   - Go to Result Management → Exams
   - Create exam schedules
   - Specify exam type and dates

6. **Set Up Fee Structure**
   - Go to Financial Management → Fee Structure
   - Define fee types and amounts for each class
   - Set due dates and academic year

### Entering Results (Teacher)

1. **Login as Teacher**
   - Use your assigned username and password

2. **Select Exam and Subject**
   - Go to Result Entry
   - Choose the exam from dropdown
   - Select your subject
   - Click "Load Students"

3. **Enter Marks**
   - Enter marks for each student
   - Grades are calculated automatically
   - System validates marks against maximum marks

4. **Save Results**
   - Click "Save Results"
   - System will validate all entries
   - Confirmation message will appear

### Recording Payments (Admin)

1. **Go to Financial Management → Payments**

2. **Select Student**
   - Choose student from dropdown
   - System loads applicable fee types

3. **Record Payment**
   - Select fee type
   - Enter amount paid
   - Choose payment method
   - Add receipt number (optional)
   - Click "Record Payment"

### Viewing Results (Student)

1. **Login as Student**
   - Use your assigned username and password

2. **View Results**
   - Go to "My Results"
   - Results are organized by exam
   - Each tab shows subjects, marks, and grades
   - Summary shows total marks and percentage

3. **Check Fee Status**
   - Go to "Fee Status"
   - View outstanding balances
   - Check payment history

## 🎯 Tips for Effective Use

### For Administrators
- Regularly backup the database file (`student_management.db`)
- Create user accounts in batches for efficiency
- Set up fee structures at the beginning of each academic year
- Use the dashboard to monitor system usage

### For Teachers
- Enter results promptly after exams
- Double-check marks before saving
- Use the grade preview to verify calculations
- Update results only when necessary to maintain data integrity

### For Students
- Check results regularly after exams
- Monitor fee status to avoid late payments
- Keep login credentials secure
- Update profile information when needed

## 🔧 System Maintenance

### Database Backup
- Copy `student_management.db` to a safe location
- Backup regularly, especially before major updates

### User Account Management
- Deactivate accounts for users who leave
- Regularly review and clean up inactive accounts
- Change default passwords immediately

### Performance Optimization
- The system is designed for small to medium institutions
- For large datasets, consider periodic cleanup of old records
- Monitor disk space usage

## 🆘 Troubleshooting

### Common Issues

**Login Problems**
- Verify username and password
- Check if account is active
- Contact administrator for password reset

**Result Entry Issues**
- Ensure exam and subject are selected
- Check that marks are within valid range
- Verify you have permission to enter results for the subject

**Payment Recording Problems**
- Confirm student and fee type selection
- Verify amount is positive number
- Check that fee structure exists for the class

**Display Issues**
- Ensure screen resolution is at least 1024x768
- Try maximizing the window
- Check system scaling settings

### Getting Help
1. Run the test script to check system integrity
2. Check console output for error messages
3. Verify all files are present in the project directory
4. Ensure proper file permissions

## 📊 Reports and Analytics

### Available Reports
- Monthly fee collection summaries
- Class-wise result analysis
- Outstanding fee reports
- Student performance tracking

### Generating Reports
1. Navigate to the Reports section
2. Select report type and parameters
3. View results in the text area
4. Copy data for external analysis if needed

## 🔒 Security Best Practices

- Change default admin password immediately
- Use strong passwords for all accounts
- Regularly review user access permissions
- Keep the system updated
- Backup data regularly
- Limit physical access to the system

---

**Support**: This system is designed to be intuitive and user-friendly. Most functions include helpful validation and error messages to guide users through the process.
