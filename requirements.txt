# Student Result Management System Requirements
# This system uses only Python built-in modules

# Core Python modules (built-in, no installation needed):
# - tkinter (GUI framework)
# - sqlite3 (database)
# - hashlib (password hashing)
# - datetime (date/time handling)
# - os (operating system interface)
# - sys (system-specific parameters)

# No external dependencies required!
# The system is designed to work with a standard Python installation.

# Minimum Python version: 3.7+
# Recommended Python version: 3.8+

# To run the system:
# 1. Ensure Python 3.7+ is installed
# 2. Run: python main.py
# 3. Or run tests first: python test_system.py
